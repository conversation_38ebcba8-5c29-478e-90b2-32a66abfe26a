```shell
## 新建虚拟机

```SHELL
## 查询区域
curl 'https://*************:30001/api/v2/cloudregions?cloud_env=onpremise&usable=true&show_emulated=true&scope=project' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "data": [
        {
            "_i18n": {},
            "can_delete": false,
            "can_update": true,
            "created_at": "2025-08-11T11:15:30.000000Z",
            "deleted": false,
            "description": "Default Region",
            "enabled": true,
            "guest_count": 4,
            "guest_increment_count": 4,
            "id": "default",
            "imported_at": "2025-08-11T11:15:30.000000Z",
            "is_emulated": false,
            "latitude": 0,
            "longitude": 0,
            "name": "Default",
            "network_count": 6,
            "progress": 100,
            "provider": "OneCloud",
            "source": "local",
            "status": "inservice",
            "update_version": 0,
            "updated_at": "2025-08-11T11:15:30.000000Z",
            "vpc_count": 1,
            "zone_count": 1
        }
    ],
    "limit": 2048,
    "total": 1
}

## 查询可用区
curl 'https://*************:30001/api/v2/zones?usable=true&show_emulated=true&order_by=created_at&order=asc&scope=project&cloudregion_id=default' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "data": [
        {
            "_i18n": {},
            "baremetals": 2,
            "baremetals_enabled": 2,
            "can_delete": false,
            "can_update": true,
            "cloudregion": "Default",
            "cloudregion_id": "default",
            "created_at": "2025-08-11T11:16:03.000000Z",
            "deleted": false,
            "hosts": 4,
            "hosts_enabled": 4,
            "id": "539598c8-6bd9-45fb-8eae-b1b81201c88d",
            "imported_at": "2025-08-11T11:16:03.000000Z",
            "is_emulated": false,
            "name": "zone0",
            "networks": 6,
            "progress": 100,
            "provider": "OneCloud",
            "region": "Default",
            "region_id": "default",
            "source": "local",
            "status": "enable",
            "storages": 4,
            "update_version": 0,
            "updated_at": "2025-08-11T11:16:03.000000Z",
            "wires": 1
        }
    ],
    "limit": 2048,
    "total": 1
}

## 查询镜像
curl 'https://*************:30001/api/v1/images?limit=0&details=true&status=active&is_guest_image=false&scope=project&os_arch=x86&filter.0=disk_format.notequals%28iso%29&is_standard=true' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {"data":[]}

## 查询区域可用容量
curl 'https://*************:30001/api/v2/cloudregions/default/capability?show_emulated=true&resource_type=shared&scope=project' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "auto_alloc_network_count": 1,
    "available_host_count": 2,
    "brands": [
        "OneCloud"
    ],
    "compute_engine_brands": [
        "OneCloud"
    ],
    "data_storage_types": [
        "local/ssd"
    ],
    "data_storage_types2": {
        "kvm": [
            "local/ssd"
        ]
    },
    "data_storage_types3": {
        "kvm": {
            "local/ssd": {
                "capacity": 1057993,
                "free_capacity": 924873,
                "is_sys_disk_store": false,
                "reserved": 0,
                "storages": [
                    {
                        "id": "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92",
                        "name": "host_192.168.15.98_local_storage_0"
                    },
                    {
                        "id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "name": "host_**************_local_storage_0"
                    }
                ],
                "used_capacity": 133120,
                "virtual_capacity": 1057993,
                "waste_capacity": 0
            }
        }
    },
    "disabled_brands": [],
    "enabled_brands": [],
    "gpu_models": [
        "Device",
        "GeForce RTX 3060"
    ],
    "host_cpu_archs": [
        "x86_64"
    ],
    "hypervisors": [
        "baremetal",
        "kvm"
    ],
    "instance_capabilities": [
        {
            "default_account": {
                "linux": {
                    "changeable": false
                },
                "windows": {
                    "changeable": false
                }
            },
            "hypervisor": "baremetal",
            "provider": "OneCloud"
        },
        {
            "default_account": {
                "linux": {
                    "changeable": true,
                    "default_account": "root"
                },
                "windows": {
                    "changeable": false,
                    "default_account": "Administrator"
                }
            },
            "hypervisor": "kvm",
            "provider": "OneCloud",
            "storages": {
                "data_disk": [
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "local"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "rbd"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "nfs"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "gpfs"
                    }
                ],
                "sys_disk": [
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "local"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "rbd"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "nfs"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "gpfs"
                    }
                ]
            }
        }
    ],
    "max_data_disk_count": 12,
    "max_nic_count": 8,
    "min_data_disk_count": 0,
    "min_nic_count": 1,
    "network_manage_brands": [
        "OneCloud"
    ],
    "pci_model_types": [
        {
            "dev_type": "GPU-HPC",
            "model": "Device",
            "size_mb": 0
        },
        {
            "dev_type": "GPU-HPC",
            "model": "GeForce RTX 3060",
            "size_mb": 0
        }
    ],
    "public_network_count": 1,
    "resource_types": [
        "shared"
    ],
    "sched_policy_support": true,
    "security_group_brands": [
        "OneCloud"
    ],
    "snapshot_policy_brands": [
        "OneCloud"
    ],
    "specs": {
        "hosts": {
            "cpu:32/manufacture:ASUS/mem:31895M/model:System_Product_Name/nic:1": {
                "count": 1,
                "cpu": 32,
                "manufacture": "ASUS",
                "mem": 31895,
                "model": "System_Product_Name",
                "nic_count": 1
            },
            "cpu:64/manufacture:ASUSTeK_COMPUTER_INC./mem:96407M/model:KRPA-U16_Series/nic:3": {
                "count": 1,
                "cpu": 64,
                "manufacture": "ASUSTeK_COMPUTER_INC.",
                "mem": 96407,
                "model": "KRPA-U16_Series",
                "nic_count": 3
            }
        },
        "isolated_devices": {}
    },
    "storage_types": [
        "local/ssd"
    ],
    "storage_types2": {
        "kvm": [
            "local/ssd"
        ]
    },
    "storage_types3": {
        "kvm": {
            "local/ssd": {
                "capacity": 1057993,
                "free_capacity": 924873,
                "is_sys_disk_store": false,
                "reserved": 0,
                "storages": [
                    {
                        "id": "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92",
                        "name": "host_192.168.15.98_local_storage_0"
                    },
                    {
                        "id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "name": "host_**************_local_storage_0"
                    }
                ],
                "used_capacity": 133120,
                "virtual_capacity": 1057993,
                "waste_capacity": 0
            }
        }
    },
    "usable": true
}

## 查询主机套餐规格
curl 'https://*************:30001/api/v2/serverskus/instance-specs?usable=true&enabled=true&cloudregion=default' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "cpu_mems_mb": {
        "1": [
            1024,
            2048,
            4096,
            8192
        ],
        "12": [
            12288,
            16384,
            24576,
            32768,
            65536
        ],
        "16": [
            16384,
            24576,
            32768,
            49152,
            65536
        ],
        "2": [
            2048,
            4096,
            8192,
            12288,
            16384
        ],
        "24": [
            24576,
            32768,
            49152,
            65536,
            131072
        ],
        "32": [
            32768,
            49152,
            65536,
            131072
        ],
        "4": [
            4096,
            12288,
            16384,
            24576,
            32768
        ],
        "8": [
            8192,
            16384,
            24576,
            32768,
            65536
        ]
    },
    "cpus": [
        1,
        2,
        4,
        8,
        12,
        16,
        24,
        32
    ],
    "mems_mb": [
        1024,
        2048,
        4096,
        8192,
        12288,
        16384,
        24576,
        32768,
        49152,
        65536,
        131072
    ]
}

## 
curl 'https://*************:30001/api/v2/serverskus/distinct-field?public_cloud=false&postpaid_status=available&cpu_core_count=2&memory_size_mb=2048&cloudregion=default&provider=OneCloud&scope=project&field=local_category' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {"local_category":["general_purpose"]}

  ## 选中CPU核数和内存需要重新查询套餐
  curl 'https://*************:30001/api/v2/serverskus?limit=10&public_cloud=false&postpaid_status=available&cpu_core_count=4&memory_size_mb=4096&cloudregion=default&provider=OneCloud&scope=project&offset=0&distinct=true&enabled=true' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  ## 好像是用来查询某个字段 distinct 这个请求是查询 local_category 
  ## 对应套餐分类[ 全部 通用型 计算优化型 内存优化型]
  curl 'https://*************:30001/api/v2/serverskus/distinct-field?public_cloud=false&postpaid_status=available&cpu_core_count=8&memory_size_mb=8192&cloudregion=default&provider=OneCloud&scope=project&field=local_category' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {"local_category":["general_purpose"]}

## 每次调整配置都要重新调用 serverskus
curl 'https://*************:30001/api/v2/serverskus?limit=10&public_cloud=false&postpaid_status=available&cpu_core_count=8&memory_size_mb=8192&cloudregion=default&provider=OneCloud&scope=project&offset=0&%40local_category=general_purpose&distinct=true&enabled=true' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure
  {
    "data": [
        {
            "attached_disk_count": 0,
            "attached_disk_size_gb": 0,
            "can_delete": false,
            "can_update": true,
            "cloudregion": "Default",
            "cloudregion_id": "default",
            "cpu_core_count": 8,
            "created_at": "2025-08-11T11:15:30.000000Z",
            "data_disk_max_count": 0,
            "deleted": false,
            "enabled": true,
            "gpu_attachable": true,
            "gpu_max_count": 0,
            "id": "1625f56c-3aca-494f-8e04-d90f67d6c2fd",
            "imported_at": "2025-08-11T11:15:30.000000Z",
            "instance_type_category": "general_purpose",
            "instance_type_family": "g1",
            "is_emulated": false,
            "local_category": "general_purpose",
            "memory_size_mb": 8192,
            "name": "ecs.g1.c8m8",
            "nic_max_count": 1,
            "os_name": "Any",
            "postpaid_status": "available",
            "prepaid_status": "available",
            "progress": 100,
            "provider": "OneCloud",
            "region": "Default",
            "region_id": "default",
            "source": "local",
            "status": "init",
            "sys_disk_max_size_gb": 0,
            "sys_disk_min_size_gb": 0,
            "sys_disk_resizable": true,
            "total_guest_count": 1,
            "update_version": 0,
            "updated_at": "2025-08-11T11:15:30.000000Z"
        }
    ],
    "limit": 10,
    "total": 1
}

## 不知用来干啥的
curl 'https://*************:30001/api/v1/monitorresourcealerts?scope=project&limit=20&alerting=true' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

## 查询自定义镜像（系统架构和磁盘格式需要符合主机套餐）
curl 'https://*************:30001/api/v1/images?limit=0&details=true&status=active&is_guest_image=false&scope=project&os_arch=x86&filter.0=disk_format.notequals%28iso%29&owner=30d3c0f5ef354a088fd64b5aa98aec10&is_standard=false' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "data": [
        {
            "can_delete": false,
            "can_update": true,
            "checksum": "503ff90b9ec3e7af3989ef2faed0f4cc",
            "created_at": "2025-08-15T11:49:52.000000Z",
            "deleted": false,
            "disable_delete": true,
            "disk_format": "qcow2",
            "domain_id": "default",
            "freezed": false,
            "id": "a4efe102-0af4-431a-86ea-eac973e659bd",
            "is_data": false,
            "is_emulated": false,
            "is_guest_image": false,
            "is_public": false,
            "is_standard": false,
            "is_system": false,
            "metadata": {
                "generate_name": "ubuntu-nvidia-22.04"
            },
            "min_disk": 30720,
            "min_ram": 0,
            "name": "ubuntu-nvidia-22.04",
            "os_arch": "x86_64",
            "oss_checksum": "503ff90b9ec3e7af3989ef2faed0f4cc",
            "pending_deleted": false,
            "progress": 0,
            "project": "system",
            "project_domain": "Default",
            "project_src": "local",
            "properties": {
                "arch": "x86_64",
                "distro": "Ubuntu",
                "installed_cloud_init": "true",
                "is_lvm_partition": "false",
                "is_readonly": "false",
                "notes": "",
                "os_arch": "x86_64",
                "os_distribution": "Ubuntu",
                "os_language": "",
                "os_type": "Linux",
                "os_version": "22.04",
                "partition_type": "",
                "uefi_support": "true",
                "version": "22.04"
            },
            "protected": true,
            "public_scope": "none",
            "public_src": "local",
            "size": 19714565632,
            "status": "active",
            "tenant": "system",
            "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
            "update_version": 24,
            "updated_at": "2025-08-15T12:09:27.000000Z"
        },
        {
            "can_delete": false,
            "can_update": true,
            "checksum": "6c1d22d04cc03ef019ac9394c0c88ed4",
            "created_at": "2025-08-14T13:00:48.000000Z",
            "deleted": false,
            "disable_delete": true,
            "disk_format": "qcow2",
            "domain_id": "default",
            "freezed": false,
            "id": "0c603a3d-9c52-479e-88b3-332a1a0d83f8",
            "is_data": false,
            "is_emulated": false,
            "is_guest_image": false,
            "is_public": false,
            "is_standard": false,
            "is_system": false,
            "min_disk": 12492,
            "min_ram": 0,
            "name": "ubuntu22.04-nvidia-full",
            "os_arch": "x86_64",
            "oss_checksum": "6c1d22d04cc03ef019ac9394c0c88ed4",
            "pending_deleted": false,
            "progress": 0,
            "project": "system",
            "project_domain": "Default",
            "project_src": "local",
            "properties": {
                "arch": "x86_64",
                "distro": "Ubuntu",
                "installed_cloud_init": "true",
                "is_lvm_partition": "false",
                "is_readonly": "false",
                "os_arch": "x86_64",
                "os_distribution": "Ubuntu",
                "os_language": "",
                "os_type": "Linux",
                "os_version": "22.04",
                "partition_type": "",
                "uefi_support": "true",
                "version": "22.04"
            },
            "protected": true,
            "public_scope": "none",
            "public_src": "local",
            "size": 11226513408,
            "status": "active",
            "tenant": "system",
            "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
            "update_version": 63,
            "updated_at": "2025-08-14T13:05:55.000000Z"
        },
        {
            "can_delete": false,
            "can_update": true,
            "checksum": "6f30f47028d05a888f4aa9874b04b48a",
            "created_at": "2025-08-13T08:15:58.000000Z",
            "deleted": false,
            "disable_delete": true,
            "disk_format": "qcow2",
            "domain_id": "default",
            "freezed": false,
            "id": "293a8271-9240-47d7-8ce3-16b89a1b6738",
            "is_data": false,
            "is_emulated": false,
            "is_guest_image": false,
            "is_public": false,
            "is_standard": false,
            "is_system": false,
            "min_disk": 8396,
            "min_ram": 0,
            "name": "ubuntu22.04-nvidia",
            "os_arch": "x86_64",
            "oss_checksum": "6f30f47028d05a888f4aa9874b04b48a",
            "pending_deleted": false,
            "progress": 0,
            "project": "system",
            "project_domain": "Default",
            "project_src": "local",
            "properties": {
                "arch": "x86_64",
                "distro": "Ubuntu",
                "installed_cloud_init": "true",
                "is_lvm_partition": "false",
                "is_readonly": "false",
                "os_arch": "x86_64",
                "os_distribution": "Ubuntu",
                "os_language": "",
                "os_type": "Linux",
                "os_version": "22.04",
                "partition_type": "",
                "uefi_support": "true",
                "version": "22.04"
            },
            "protected": true,
            "public_scope": "none",
            "public_src": "local",
            "size": 3214475264,
            "status": "active",
            "tenant": "system",
            "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
            "update_version": 16,
            "updated_at": "2025-08-13T08:16:53.000000Z"
        },
        {
            "can_delete": false,
            "can_update": true,
            "checksum": "7908c29d39c9c8d8b28bb9ba5d508fbd",
            "created_at": "2025-08-11T11:51:44.000000Z",
            "deleted": false,
            "disable_delete": true,
            "disk_format": "qcow2",
            "domain_id": "default",
            "freezed": false,
            "id": "53ca1322-962d-44a0-8d38-cbddb925aa29",
            "is_data": false,
            "is_emulated": false,
            "is_guest_image": false,
            "is_public": false,
            "is_standard": false,
            "is_system": false,
            "min_disk": 30720,
            "min_ram": 0,
            "name": "CentOS-7.6.1810-20190430",
            "os_arch": "x86",
            "oss_checksum": "7908c29d39c9c8d8b28bb9ba5d508fbd",
            "pending_deleted": false,
            "progress": 0,
            "project": "system",
            "project_domain": "Default",
            "project_src": "local",
            "properties": {
                "arch": "x86_64",
                "disk_driver": "",
                "distro": "CentOS",
                "installed_cloud_init": "false",
                "is_lvm_partition": "false",
                "is_readonly": "false",
                "net_driver": "",
                "os_arch": "x86",
                "os_distribution": "CentOS",
                "os_language": "",
                "os_type": "Linux",
                "os_version": "7.6.1810",
                "partition_type": "",
                "uefi_support": "true",
                "vdi_protocol": "vnc",
                "version": "7.6.1810"
            },
            "protected": true,
            "public_scope": "none",
            "public_src": "local",
            "size": 893845504,
            "status": "active",
            "tenant": "system",
            "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
            "update_version": 11,
            "updated_at": "2025-08-12T11:25:09.000000Z"
        },
        {
            "can_delete": false,
            "can_update": true,
            "checksum": "778bcef0e8248b737ca4d63c62bf9f5a",
            "created_at": "2025-08-11T11:51:16.000000Z",
            "deleted": false,
            "disable_delete": true,
            "disk_format": "qcow2",
            "domain_id": "default",
            "freezed": false,
            "id": "592f45f3-d350-4821-88b6-a4d7eff24c88",
            "is_data": false,
            "is_emulated": false,
            "is_guest_image": false,
            "is_public": false,
            "is_standard": false,
            "is_system": false,
            "min_disk": 2252,
            "min_ram": 0,
            "name": "Ubuntu Server-Linux-22.04(Jammy)-x86_64-current",
            "os_arch": "x86_64",
            "oss_checksum": "778bcef0e8248b737ca4d63c62bf9f5a",
            "pending_deleted": false,
            "progress": 0,
            "project": "system",
            "project_domain": "Default",
            "project_src": "local",
            "properties": {
                "arch": "x86_64",
                "distro": "Ubuntu",
                "installed_cloud_init": "true",
                "is_lvm_partition": "false",
                "is_readonly": "false",
                "os_arch": "x86_64",
                "os_distribution": "Ubuntu",
                "os_language": "",
                "os_type": "Linux",
                "os_version": "22.04",
                "partition_type": "",
                "uefi_support": "true",
                "version": "22.04"
            },
            "protected": true,
            "public_scope": "none",
            "public_src": "local",
            "size": 677302272,
            "status": "active",
            "tenant": "system",
            "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
            "update_version": 21,
            "updated_at": "2025-08-11T11:52:38.000000Z"
        }
    ],
    "limit": 2048,
    "total": 5
}

## 创建服务器
curl 'https://*************:30001/api/v2/servers' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json;charset=UTF-8' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'origin: https://*************:30001' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --data-raw '{"auto_start":true,"generate_name":"host01_test_wu","description":"","hypervisor":"kvm","__count__":1,"disks":[{"disk_type":"sys","index":0,"backend":"local","size":30720,"image_id":"a4efe102-0af4-431a-86ea-eac973e659bd","medium":"ssd"}],"nets":[{"exit":false}],"prefer_region":"default","project_id":"30d3c0f5ef354a088fd64b5aa98aec10","os_arch":"x86","sku":"ecs.g1.c8m8","password":"Aa123456789@","prefer_zone":"539598c8-6bd9-45fb-8eae-b1b81201c88d","bios":"","vdi":"","vga":"","machine":"pc","duration":"1h","billing_type":"postpaid","deploy_telegraf":true}' \
  --insecure

  {
    "auto_start": true,
    "generate_name": "host01_test_wu",
    "description": "",
    "hypervisor": "kvm",
    "__count__": 1,
    "disks": [
        {
            "disk_type": "sys",
            "index": 0,
            "backend": "local",
            "size": 30720,
            "image_id": "a4efe102-0af4-431a-86ea-eac973e659bd",
            "medium": "ssd"
        }
    ],
    "nets": [
        {
            "exit": false
        }
    ],
    "prefer_region": "default",
    "project_id": "30d3c0f5ef354a088fd64b5aa98aec10",
    "os_arch": "x86",
    "sku": "ecs.g1.c8m8",
    "password": "Aa123456789@",
    "prefer_zone": "539598c8-6bd9-45fb-8eae-b1b81201c88d",
    "bios": "",
    "vdi": "",
    "vga": "",
    "machine": "pc",
    "duration": "1h",
    "billing_type": "postpaid",
    "deploy_telegraf": true
}

## 调整配置
curl 'https://*************:30001/api/v2/servers/change-config?id=5bfc24d4-6ab6-47b1-8cd2-37863fcb4047' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json;charset=UTF-8' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'origin: https://*************:30001' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/adjust-config?id=5bfc24d4-6ab6-47b1-8cd2-37863fcb4047' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --data-raw '{"sku":"ecs.g1.c12m12","auto_start":true,"disks":[{"disk_type":"data","index":1,"size":10240,"backend":"local","medium":"ssd"}]}' \
  --insecure

  {
    "data": [
        {
            "data": {
                "account_read_only": false,
                "auto_renew": false,
                "backup_guest_status": "init",
                "billing_cycle": "1H",
                "billing_type": "postpaid",
                "bios": "UEFI",
                "boot_order": "cdn",
                "brand": "OneCloud",
                "can_delete": false,
                "can_recycle": false,
                "can_update": true,
                "cdrom_support": true,
                "cloud_env": "onpremise",
                "cloudregion": "Default",
                "cloudregion_id": "default",
                "cpu_sockets": 1,
                "created_at": "2025-08-17T02:11:11.000000Z",
                "deleted": false,
                "disable_delete": true,
                "disk": 30720,
                "disk_count": 1,
                "disks": "disk0:30720M/qcow2/scsi/none/native/root",
                "disks_info": [
                    {
                        "aio_mode": "native",
                        "auto_reset": false,
                        "boot_index": -1,
                        "bps": 0,
                        "cache_mode": "none",
                        "disk_format": "qcow2",
                        "disk_type": "sys",
                        "driver": "scsi",
                        "id": "b8fae7f6-a844-44ef-86d5-83251dc287ad",
                        "image": "ubuntu-nvidia-22.04",
                        "image_id": "a4efe102-0af4-431a-86ea-eac973e659bd",
                        "index": 0,
                        "iops": 0,
                        "medium_type": "ssd",
                        "name": "vdisk-host01_test_wu-1755396671825129335",
                        "preallocation": "off",
                        "size": 30720,
                        "storage_id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "storage_type": "local",
                        "throughput": 0
                    }
                ],
                "domain_id": "default",
                "expired_at": "2025-08-17T03:13:07.000000Z",
                "floppy_support": true,
                "freezed": false,
                "host": "service-192-168-15-253",
                "host_access_ip": "**************",
                "host_billing_type": "postpaid",
                "host_enabled": true,
                "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
                "host_resource_type": "shared",
                "host_service_status": "online",
                "host_sn": "System Serial Number",
                "host_status": "online",
                "host_type": "hypervisor",
                "hostname": "host01testwu",
                "hypervisor": "kvm",
                "id": "5bfc24d4-6ab6-47b1-8cd2-37863fcb4047",
                "imported_at": "2025-08-17T02:11:11.000000Z",
                "instance_type": "ecs.g1.c8m8",
                "internet_max_bandwidth_out": 0,
                "ips": "*************",
                "is_daemon": false,
                "is_emulated": false,
                "is_gpu": false,
                "is_prepaid_recycle": false,
                "is_system": false,
                "machine": "pc",
                "macs": "00:22:bd:bc:cb:bd",
                "metadata": {
                    "app_tags": "",
                    "generate_name": "host01_test_wu",
                    "hot_remove_nic": "enable",
                    "hotplug_cpu_mem": "enable",
                    "last_login_key": "pwatOI45dLAWnmqcxwWPKTN4cZFSzgD6pcxB4Q==",
                    "login_account": "root",
                    "login_key": "pwatOI45dLAWnmqcxwWPKTN4cZFSzgD6pcxB4Q==",
                    "login_key_timestamp": "2025-08-17T02:13:07.654959Z",
                    "os_arch": "x86_64",
                    "os_distribution": "Ubuntu",
                    "os_name": "Linux",
                    "os_version": "22.04",
                    "start_vcpu_count": "8",
                    "start_vmem_mb": "8192",
                    "telegraf_deployed": "true"
                },
                "monitor_url": "http://***************/monitor",
                "name": "host01_test_wu",
                "networks": "eth0:*************/21/00:22:bd:bc:cb:bd/1/vm-net/virtio/1000/0\n",
                "nics": [
                    {
                        "ip_addr": "*************",
                        "is_exit": false,
                        "mac": "00:22:bd:bc:cb:bd",
                        "network_id": "3836a1dd-9edc-43f0-8648-33adafb71202",
                        "vpc_id": "default"
                    }
                ],
                "os_arch": "x86",
                "os_name": "Linux",
                "os_type": "Linux",
                "pending_deleted": false,
                "power_states": "on",
                "progress": 0,
                "progress_mbps": 294.64453125,
                "project": "system",
                "project_domain": "Default",
                "project_src": "local",
                "provider": "OneCloud",
                "qga_status": "unknown",
                "region": "Default",
                "region_id": "default",
                "release_at": "2025-08-17T03:11:11.000000Z",
                "rescue_mode": false,
                "secgroup": "Default",
                "secgroups": [
                    {
                        "id": "default",
                        "name": "Default"
                    }
                ],
                "secgrp_id": "default",
                "security_rules": "in:allow any",
                "shutdown_behavior": "stop",
                "shutdown_mode": "keep_charging",
                "source": "local",
                "src_ip_check": true,
                "src_mac_check": true,
                "sshable_last_state": false,
                "status": "change_flavor",
                "tenant": "system",
                "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
                "throughput": 0,
                "update_version": 79,
                "updated_at": "2025-08-17T02:18:55.000000Z",
                "vcpu_count": 8,
                "vmem_size": 8192,
                "vpc": "Default",
                "vpc_external_access_mode": "eip-distgw",
                "vpc_id": "default",
                "zone": "zone0",
                "zone_id": "539598c8-6bd9-45fb-8eae-b1b81201c88d"
            },
            "id": "5bfc24d4-6ab6-47b1-8cd2-37863fcb4047",
            "status": 200
        }
    ]
}

## 关机
curl 'https://*************:30001/api/v2/servers/stop?id=5bfc24d4-6ab6-47b1-8cd2-37863fcb4047' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json;charset=UTF-8' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'origin: https://*************:30001' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --data-raw '{"is_force":false}' \
  --insecure

  {
    "data": [
        {
            "data": {
                "account_read_only": false,
                "auto_renew": false,
                "backup_guest_status": "init",
                "billing_cycle": "1H",
                "billing_type": "postpaid",
                "bios": "UEFI",
                "boot_order": "cdn",
                "brand": "OneCloud",
                "can_delete": false,
                "can_recycle": false,
                "can_update": true,
                "cdrom_support": true,
                "cloud_env": "onpremise",
                "cloudregion": "Default",
                "cloudregion_id": "default",
                "cpu_sockets": 1,
                "created_at": "2025-08-17T02:11:11.000000Z",
                "deleted": false,
                "disable_delete": true,
                "disk": 40960,
                "disk_count": 2,
                "disks": "disk0:30720M/qcow2/scsi/none/native/root\ndisk1:10240M/qcow2/scsi/none/native/none",
                "disks_info": [
                    {
                        "aio_mode": "native",
                        "auto_reset": false,
                        "boot_index": -1,
                        "bps": 0,
                        "cache_mode": "none",
                        "disk_format": "qcow2",
                        "disk_type": "sys",
                        "driver": "scsi",
                        "id": "b8fae7f6-a844-44ef-86d5-83251dc287ad",
                        "image": "ubuntu-nvidia-22.04",
                        "image_id": "a4efe102-0af4-431a-86ea-eac973e659bd",
                        "index": 0,
                        "iops": 0,
                        "medium_type": "ssd",
                        "name": "vdisk-host01_test_wu-1755396671825129335",
                        "preallocation": "off",
                        "size": 30720,
                        "storage_id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "storage_type": "local",
                        "throughput": 0
                    },
                    {
                        "aio_mode": "native",
                        "auto_reset": false,
                        "boot_index": -1,
                        "bps": 0,
                        "cache_mode": "none",
                        "disk_format": "qcow2",
                        "disk_type": "data",
                        "driver": "scsi",
                        "id": "93f8af59-43b8-4377-826d-a2565e721fe5",
                        "index": 1,
                        "iops": 0,
                        "medium_type": "ssd",
                        "name": "vdisk-host01_test_wu-1755397135873251796",
                        "preallocation": "off",
                        "size": 10240,
                        "storage_id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "storage_type": "local",
                        "throughput": 0
                    }
                ],
                "domain_id": "default",
                "expired_at": "2025-08-17T03:13:07.000000Z",
                "floppy_support": true,
                "freezed": false,
                "host": "service-192-168-15-253",
                "host_access_ip": "**************",
                "host_billing_type": "postpaid",
                "host_enabled": true,
                "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
                "host_resource_type": "shared",
                "host_service_status": "online",
                "host_sn": "System Serial Number",
                "host_status": "online",
                "host_type": "hypervisor",
                "hostname": "host01testwu",
                "hypervisor": "kvm",
                "id": "5bfc24d4-6ab6-47b1-8cd2-37863fcb4047",
                "imported_at": "2025-08-17T02:11:11.000000Z",
                "instance_type": "ecs.g1.c12m12",
                "internet_max_bandwidth_out": 0,
                "ips": "*************",
                "is_daemon": false,
                "is_emulated": false,
                "is_gpu": false,
                "is_prepaid_recycle": false,
                "is_system": false,
                "machine": "pc",
                "macs": "00:22:bd:bc:cb:bd",
                "metadata": {
                    "app_tags": "",
                    "generate_name": "host01_test_wu",
                    "hot_remove_nic": "enable",
                    "hotplug_cpu_mem": "enable",
                    "last_login_key": "pwatOI45dLAWnmqcxwWPKTN4cZFSzgD6pcxB4Q==",
                    "login_account": "root",
                    "login_key": "pwatOI45dLAWnmqcxwWPKTN4cZFSzgD6pcxB4Q==",
                    "login_key_timestamp": "2025-08-17T02:13:07.654959Z",
                    "os_arch": "x86_64",
                    "os_distribution": "Ubuntu",
                    "os_name": "Linux",
                    "os_version": "22.04",
                    "start_vcpu_count": "8",
                    "start_vmem_mb": "8192",
                    "telegraf_deployed": "true"
                },
                "monitor_url": "http://***************/monitor",
                "name": "host01_test_wu",
                "networks": "eth0:*************/21/00:22:bd:bc:cb:bd/1/vm-net/virtio/1000/0\n",
                "nics": [
                    {
                        "ip_addr": "*************",
                        "is_exit": false,
                        "mac": "00:22:bd:bc:cb:bd",
                        "network_id": "3836a1dd-9edc-43f0-8648-33adafb71202",
                        "vpc_id": "default"
                    }
                ],
                "os_arch": "x86",
                "os_name": "Linux",
                "os_type": "Linux",
                "pending_deleted": false,
                "power_states": "on",
                "progress": 0,
                "progress_mbps": 294.64453125,
                "project": "system",
                "project_domain": "Default",
                "project_src": "local",
                "provider": "OneCloud",
                "qga_status": "unknown",
                "region": "Default",
                "region_id": "default",
                "release_at": "2025-08-17T03:11:11.000000Z",
                "rescue_mode": false,
                "secgroup": "Default",
                "secgroups": [
                    {
                        "id": "default",
                        "name": "Default"
                    }
                ],
                "secgrp_id": "default",
                "security_rules": "in:allow any",
                "shutdown_behavior": "stop",
                "shutdown_mode": "keep_charging",
                "source": "local",
                "src_ip_check": true,
                "src_mac_check": true,
                "sshable_last_state": false,
                "status": "start_stop",
                "tenant": "system",
                "tenant_id": "30d3c0f5ef354a088fd64b5aa98aec10",
                "throughput": 0,
                "update_version": 83,
                "updated_at": "2025-08-17T02:28:53.000000Z",
                "vcpu_count": 12,
                "vmem_size": 12288,
                "vpc": "Default",
                "vpc_external_access_mode": "eip-distgw",
                "vpc_id": "default",
                "zone": "zone0",
                "zone_id": "539598c8-6bd9-45fb-8eae-b1b81201c88d"
            },
            "id": "5bfc24d4-6ab6-47b1-8cd2-37863fcb4047",
            "status": 200
        }
    ]
}

## 虚拟机列表

curl 'https://*************:30001/api/v2/servers?scope=project&show_fail_reason=true&details=true&with_meta=true&filter=hypervisor.notin%28baremetal%2Ccontainer%29&summary_stats=true&limit=20' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE3VDA5OjU3OjM2WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ETmVuMG5mblFMUDZ1NGJjbVR3andDYWpLQy1JNEhLUHVXMllSang0TkxmOU9jaXMxUWhCQ3ZoLXVRLUQzNzI1TEpPMFg0U09FMHFOb3NxbVNUMjlUaGVPOFJmbE8xaV9TZGEteG90bm9sRTZ6WWYxN2drVVpQMWVYcjdTckZaWWIySWN4ZEk1TUp1bkp1U0ZIdHVHNnhfYmxwUUpWUExMcmZ1UGNWZFJmUnB6X01MMW9PLWtkN3VkRFVKaWtBbzZMSDNmMFF2bzA3SW1DNkx3U3Nzc2pPcGNuS056aDFfNGdMM1MxN3I0bXVZYkMxWEcwTWo3UVJUdzAzdFpuS1UxODlacjVsNXlXTmYxSFJ6S3g4Rm1JUXFhaXhJU0hmNERPX3VnalYweU54SDk4di1CTUV6NmFmTFljODNzMmhYMFh3ZjkwZ2xZTm9DaFJuNmVoYWhvZlEuNGhMeXdtbWtZRFVvdkNoUi5qcGtTam1QR0VpdTRFUXpHS0ptMko1dk92alM1ZEk4bkhPaTA4RzM1SF80cUVEZGxkcmJGRmNTNkRwckwyRHFLX1dGVmFROVpqdmc4ZHc0N254ay5BS3NCTEh6LXo3d0JBRjhWZnhtY2JnIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=project' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  scope=project
  show_fail_reason=true
  details=true
  with_meta=true
  filter=hypervisor.notin(baremetal,container) #排除裸金属、容器
  hypervisor=baremetal #如果是裸金属列表
  summary_stats=true
  limit=20
  
 curl 'https://*************:30001/api/v1/rpc/schedulers/forecast' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json;charset=UTF-8' \
  -b 'lang=zh-CN; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=system; region=region0; yunionauth=eyJleHAiOiIyMDI1LTA4LTIxVDE1OjE3OjE4WiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5ZRGtWbGlIRzI0cXRHZVdPcEhsTHprZUZSQmNZc3JWeHVLV3gyRUM0SjZ2ZzhmRDV6b0FrNm1hWDBtX1dXSHgzV3IwdGhCbUg4NlZuRmFxV2YwU1FIZ211SWU2bHF1Rktic0IxQ2FZdFQxekN3RTVtRU1lMGREa052QWNIVG1iN21XX245MGlJN1pncUhKYVhoVHNfSnhtVFhOdkp1VjNtVzdjT3lsbjA1cEFlLXB0S01XdGV0N2ZnbHFERGJNOUxWY2txWTdyYjBkS2pRdWRCeWlIeG5CdHBFMkNVX3BHVTNUdXBMcEVVZTJzT0lkZUY0X0o0b0N6bFZRdzFoZ2lmLWxocDdhaGtDRUtVLTY0UVFXTF9MSDlPcHlSUlpkUUNzRUdTWTd0NlJOeFFiMnE1YlpLeEtSM1VZVURPbkI2Y1FBZUxLa1V5WHBER1NwYTR6Uk5wV1EuMGY3Umt5NTJkY3UtdTVvXy5hR0FqQUZWUC1lVmE3a3ptZy1aMmtRdW1lNEVab3NhU094UVo3WmxYNjlzak5QNDRmQWl3YnlLeFhwSGd2VTR0RzJZbGUxd1F1dFJqZUdDalNsREpyUS5JY1JXQjhGMnlRTUJfY0NxdnBTNm5nIiwic3lzdGVtX3RvdHBfb24iOnRydWUsInRvdHBfaW5pdCI6ZmFsc2UsInRvdHBfb24iOmZhbHNlLCJ0b3RwX3ZlcmlmaWVkIjpmYWxzZSwidXNlciI6ImFkbWluIiwidXNlcl9pZCI6IjdhYTQ2YjQxYjJmMTQ3N2Y4NjFiMmFkNjYzZWUyM2UxIn0; domain=Default' \
  -H 'dnt: 1' \
  -H 'origin: https://*************:30001' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/vminstance/create?type=idc' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --data-raw '{"auto_start":true,"generate_name":"test##","description":"","hypervisor":"kvm","__count__":10,"disks":[{"disk_type":"sys","index":0,"backend":"local","size":30720,"image_id":"53ca1322-962d-44a0-8d38-cbddb925aa29","medium":"ssd"}],"nets":[{"exit":false}],"prefer_region":"default","vcpu_count":1,"vmem_size":2048,"project_id":"30d3c0f5ef354a088fd64b5aa98aec10","os_arch":"x86","sku":"ecs.g1.c1m2","prefer_zone":"539598c8-6bd9-45fb-8eae-b1b81201c88d","deploy_telegraf":true}' \
  --insecure
  
  {
    "allow_count": 8,
    "can_create": false,
    "candidates": [
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "0a698b95-45da-41ee-8e10-c5079c0fec98"
                    ]
                }
            ],
            "error": "",
            "host_id": "8f4d0d47-63d4-47f3-841d-c7bfce2e781e",
            "name": "service-192-168-15-253",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92"
                    ]
                }
            ],
            "error": "",
            "host_id": "4bdd5107-2246-4f36-8323-7643c020f257",
            "name": "compute-192-168-15-98",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        },
        {
            "backup_candidate": null,
            "cpu_numa_pin": null,
            "disks": [
                {
                    "index": 0,
                    "storage_ids": [
                        "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92"
                    ]
                }
            ],
            "error": "",
            "host_id": "4bdd5107-2246-4f36-8323-7643c020f257",
            "name": "compute-192-168-15-98",
            "nets": [
                {
                    "index": 0,
                    "network_ids": [
                        "3836a1dd-9edc-43f0-8648-33adafb71202"
                    ]
                }
            ],
            "session_id": "1755703650822"
        }
    ],
    "filtered_candidates": [],
    "not_allow_reasons": [
        "Out of resource",
        "Out of resource"
    ],
    "req_count": 10
}
```


## 新建裸金属

```shell

## 查询区域容量
curl 'https://*************:30001/api/v2/zones/539598c8-6bd9-45fb-8eae-b1b81201c88d/capability?show_emulated=true&resource_type=shared&host_type=baremetal&project_domain%5Bkey%5D=30d3c0f5ef354a088fd64b5aa98aec10&project_domain%5Blabel%5D=system' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6' \
  -H 'cache-control: no-cache' \
  -b 'region=region0; lang=zh-CN; domain=Default; yunionauth=eyJleHAiOiIyMDI1LTA4LTE4VDAzOjEyOjEwWiIsImlzX3NzbyI6ZmFsc2UsInNlc3Npb24iOiJleUpoYkdjaU9pSlNVMEV4WHpVaUxDSjZhWEFpT2lKRVJVWWlMQ0psYm1NaU9pSkJNVEk0UjBOTkluMC5XSXhmQ1RaQUNsa0JSVEFpR0c5RjBGMFJKSDBfOEZtcWhaczk1RHFVeE1jakljN29QaWZBaURzQVc3VzV0MFRHZXBqZ1dieUtEcHVnc2pvemRwdkZ4RzE0TVA0enpMOHh5MjVpcEJkTHdIQ19MMFpQOFEtN0l3ZmJQeTFpaHJkaF9wa2N3MHhGNDJ4VWJKMktWZTVVVlNzMzZmNnNLVTJuaGpBZ2s2cjhpMXh2Mm92eXdwWGdVZFJnSGFSbTdjUE1iVmE4YXVKY0pWS0NxR3Q0eTgwSmRhLXpzMjVMck83UDFFTXZlSnl5eTJDenU2NVQwS2RzTVN6R1lUUnlpQzlkV0tVbkJaSGtaVGVvZkNJckxXZHN1aXVqallGOWhVWmlSYU5ya0t5eDFjcjY1alJCRDI2MmEwazBPZGhUaW9pMDNmTUhTWkh5RWhIbjVRVjRNOXo1T3cuWW9VbkRWR0MwcWZGYTI4Vy4tLVhLOXVEN2ZZQURiRzV6WlRwRk5oNGxwdzg0cXc1VVQ5N0RsOUNMQ19hN01FNVlmWjA1d2pqT1ZwTmxWVE03LTNIMmg2STlrYjY5UkVPS2ZMVVQuLWlqcVBQMG1LLVJXWUdVUllBRVJXUSIsInN5c3RlbV90b3RwX29uIjp0cnVlLCJ0b3RwX2luaXQiOmZhbHNlLCJ0b3RwX29uIjpmYWxzZSwidG90cF92ZXJpZmllZCI6ZmFsc2UsInVzZXIiOiJhZG1pbiIsInVzZXJfaWQiOiI3YWE0NmI0MWIyZjE0NzdmODYxYjJhZDY2M2VlMjNlMSJ9; tenant=30d3c0f5ef354a088fd64b5aa98aec10; scope=system' \
  -H 'dnt: 1' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://*************:30001/baremetal/create?type=baremetal' \
  -H 'sec-ch-ua: "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-yunion-lang: zh-CN' \
  --insecure

  {
    "auto_alloc_network_count": 0,
    "available_host_count": 2,
    "brands": [
        "OneCloud"
    ],
    "compute_engine_brands": [
        "OneCloud"
    ],
    "data_storage_types": [
        "local/ssd"
    ],
    "data_storage_types2": {
        "kvm": [
            "local/ssd"
        ]
    },
    "data_storage_types3": {
        "kvm": {
            "local/ssd": {
                "capacity": 1057993,
                "free_capacity": 1017033,
                "is_sys_disk_store": false,
                "reserved": 0,
                "storages": [
                    {
                        "id": "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92",
                        "name": "host_192.168.15.98_local_storage_0"
                    },
                    {
                        "id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "name": "host_**************_local_storage_0"
                    }
                ],
                "used_capacity": 40960,
                "virtual_capacity": 1057993,
                "waste_capacity": 0
            }
        }
    },
    "disabled_brands": [],
    "enabled_brands": [],
    "gpu_models": [
        "Device",
        "GeForce RTX 3060"
    ],
    "host_cpu_archs": [
        "x86_64"
    ],
    "hypervisors": [
        "baremetal",
        "kvm"
    ],
    "instance_capabilities": [
        {
            "default_account": {
                "linux": {
                    "changeable": false
                },
                "windows": {
                    "changeable": false
                }
            },
            "hypervisor": "baremetal",
            "provider": "OneCloud"
        },
        {
            "default_account": {
                "linux": {
                    "changeable": true,
                    "default_account": "root"
                },
                "windows": {
                    "changeable": false,
                    "default_account": "Administrator"
                }
            },
            "hypervisor": "kvm",
            "provider": "OneCloud",
            "storages": {
                "data_disk": [
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "local"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "rbd"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "nfs"
                    },
                    {
                        "max_size_gb": 10240,
                        "min_size_gb": 10,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "gpfs"
                    }
                ],
                "sys_disk": [
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "local"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "rbd"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "nfs"
                    },
                    {
                        "max_size_gb": 2048,
                        "min_size_gb": 30,
                        "resizable": true,
                        "step_size_gb": 1,
                        "storage_type": "gpfs"
                    }
                ]
            }
        }
    ],
    "max_data_disk_count": 12,
    "max_nic_count": 8,
    "min_data_disk_count": 0,
    "min_nic_count": 1,
    "network_manage_brands": [
        "OneCloud"
    ],
    "pci_model_types": [
        {
            "dev_type": "GPU-HPC",
            "model": "Device",
            "size_mb": 0
        },
        {
            "dev_type": "GPU-HPC",
            "model": "GeForce RTX 3060",
            "size_mb": 0
        }
    ],
    "public_network_count": 0,
    "resource_types": [
        "shared"
    ],
    "sched_policy_support": true,
    "security_group_brands": [
        "OneCloud"
    ],
    "snapshot_policy_brands": [
        "OneCloud"
    ],
    "specs": {
        "hosts": {},
        "isolated_devices": {}
    },
    "storage_types": [
        "local/ssd"
    ],
    "storage_types2": {
        "kvm": [
            "local/ssd"
        ]
    },
    "storage_types3": {
        "kvm": {
            "local/ssd": {
                "capacity": 1057993,
                "free_capacity": 1017033,
                "is_sys_disk_store": false,
                "reserved": 0,
                "storages": [
                    {
                        "id": "6b5f0a80-a2ca-4f93-8397-42b5db3b4b92",
                        "name": "host_192.168.15.98_local_storage_0"
                    },
                    {
                        "id": "0a698b95-45da-41ee-8e10-c5079c0fec98",
                        "name": "host_**************_local_storage_0"
                    }
                ],
                "used_capacity": 40960,
                "virtual_capacity": 1057993,
                "waste_capacity": 0
            }
        }
    },
    "usable": true
}
```