package com.cloudpod.podsail;

import com.cloudpod.podsail.service.cloudpods.CloudPodsService;
import com.cloudpod.podsail.service.cloudpods.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CloudPods集成测试
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@SpringBootTest
public class CloudPodsIntegrationTest {

    @Autowired
    private CloudPodsService cloudPodsService;

    @Test
    public void testCloudPodsConnection() {
        log.info("测试CloudPods连接");
        boolean isConnected = cloudPodsService.checkConnection();
        assertTrue(isConnected, "CloudPods连接应该成功");
    }

    @Test
    public void testGetRegionList() {
        log.info("测试查询区域列表");
        CloudPodsRegionQueryRequest queryRequest = new CloudPodsRegionQueryRequest()
                .setCloudEnv("onpremise")
                .setUsable(true)
                .setEnabled(true)
                .setLimit(10);

        CloudPodsRegionListResponse response = cloudPodsService.getRegionList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        assertNotNull(response.getRegions(), "区域列表不应为空");
        
        log.info("查询到区域数量: {}", response.getTotal());
        response.getRegions().forEach(region -> 
            log.info("区域: id={}, name={}, status={}", region.getId(), region.getName(), region.getStatus())
        );
    }

    @Test
    public void testGetZoneList() {
        log.info("测试查询可用区列表");
        CloudPodsZoneQueryRequest queryRequest = new CloudPodsZoneQueryRequest()
                .setUsable(true)
                .setEnabled(true)
                .setLimit(10);

        CloudPodsZoneListResponse response = cloudPodsService.getZoneList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        assertNotNull(response.getZones(), "可用区列表不应为空");
        
        log.info("查询到可用区数量: {}", response.getTotal());
        response.getZones().forEach(zone -> 
            log.info("可用区: id={}, name={}, region={}, status={}", 
                zone.getId(), zone.getName(), zone.getCloudregion(), zone.getStatus())
        );
    }

    @Test
    public void testGetServerSkuList() {
        log.info("测试查询服务器SKU列表");
        CloudPodsSkuQueryRequest queryRequest = new CloudPodsSkuQueryRequest()
                .setEnabled(true)
                .setLimit(20);

        CloudPodsSkuListResponse response = cloudPodsService.getServerSkuList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        assertNotNull(response.getSkus(), "SKU列表不应为空");
        
        log.info("查询到SKU数量: {}", response.getTotal());
        response.getSkus().forEach(sku -> 
            log.info("SKU: id={}, name={}, cpu={}, memory={}MB", 
                sku.getId(), sku.getName(), sku.getCpuCoreCount(), sku.getMemoryMb())
        );
    }

    @Test
    public void testGetVirtualMachineList() {
        log.info("测试查询虚拟机列表");
        CloudPodsVmQueryRequest queryRequest = new CloudPodsVmQueryRequest()
                .setHypervisor("kvm")
                .setLimit(10);

        CloudPodsVmListResponse response = cloudPodsService.getVirtualMachineList(queryRequest);
        
        assertNotNull(response, "响应不应为空");
        assertTrue(response.getSuccess(), "查询应该成功");
        
        log.info("查询到虚拟机数量: {}", response.getTotal());
        if (response.getServers() != null && !response.getServers().isEmpty()) {
            response.getServers().forEach(server -> 
                log.info("虚拟机: id={}, name={}, status={}, sku={}", 
                    server.getId(), server.getName(), server.getStatus(), server.getSku())
            );
        }
    }
}
