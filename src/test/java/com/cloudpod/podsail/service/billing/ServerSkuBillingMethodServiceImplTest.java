package com.cloudpod.podsail.service.billing;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.db.dao.ServerSkuBillingMethodDao;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.service.billing.impl.ServerSkuBillingMethodServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ServerSkuBillingMethodServiceImpl 测试类
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
public class ServerSkuBillingMethodServiceImplTest {

    @Mock
    private ServerSkuBillingMethodDao serverSkuBillingMethodDao;

    @InjectMocks
    private ServerSkuBillingMethodServiceImpl serverSkuBillingMethodService;

    private ServerSkuBillingMethodCreateDTO createDTO;
    private ServerSkuBillingMethodUpdateDTO updateDTO;
    private ServerSkuBillingMethodQueryDTO queryDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        createDTO = new ServerSkuBillingMethodCreateDTO()
                .setServerSkuId("ecs.g1.c8m8")
                .setBillingCycle("1H")
                .setUnitPrice(new BigDecimal("10.5000"))
                .setSiteId(1)
                .setRemark("测试定价");

        updateDTO = new ServerSkuBillingMethodUpdateDTO()
                .setUnitPrice(new BigDecimal("15.0000"))
                .setRemark("更新后的测试定价");

        queryDTO = new ServerSkuBillingMethodQueryDTO();
        queryDTO.setCurrent(1L);
        queryDTO.setSize(10L);
    }

    @Test
    void testCreateServerSkuBillingMethod_Success() {
        log.info("测试创建主机套餐规格定价 - 成功场景");

        // Mock 不存在重复记录
        when(serverSkuBillingMethodDao.lambdaQuery()).thenReturn(mock(LambdaQueryWrapper.class));
        LambdaQueryWrapper<ServerSkuBillingMethod> mockWrapper = mock(LambdaQueryWrapper.class);
        when(serverSkuBillingMethodDao.lambdaQuery()).thenReturn(mockWrapper);
        when(mockWrapper.eq(any(), any())).thenReturn(mockWrapper);
        when(mockWrapper.exists()).thenReturn(false);

        // Mock 保存操作
        when(serverSkuBillingMethodDao.save(any(ServerSkuBillingMethod.class))).thenReturn(true);

        ServerSkuBillingMethodResponseDTO response = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);

        assertNotNull(response, "响应不应为空");
        assertEquals(createDTO.getServerSkuId(), response.getServerSkuId(), "服务器SKU ID应该匹配");
        assertEquals(createDTO.getBillingCycle(), response.getBillingCycle(), "计费周期应该匹配");
        assertEquals(createDTO.getUnitPrice(), response.getUnitPrice(), "单价应该匹配");
        assertEquals(createDTO.getSiteId(), response.getSiteId(), "站点ID应该匹配");
        assertEquals(createDTO.getRemark(), response.getRemark(), "备注应该匹配");
        assertEquals(Integer.valueOf(1), response.getStatus(), "状态应该为1（正常）");

        // 验证 Mock 调用
        verify(serverSkuBillingMethodDao).save(any(ServerSkuBillingMethod.class));

        log.info("创建成功测试通过");
    }

    @Test
    void testCreateServerSkuBillingMethod_DuplicateThrowsException() {
        log.info("测试创建主机套餐规格定价 - 重复创建抛出异常");
        
        // 先创建一个定价
        serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 再次创建相同的定价组合，应该抛出异常
        PodSailException exception = assertThrows(PodSailException.class, () -> {
            serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        });
        
        log.info("捕获到预期异常: {}", exception.getMessage());
    }

    @Test
    void testUpdateServerSkuBillingMethod_Success() {
        log.info("测试更新主机套餐规格定价 - 成功场景");
        
        // 先创建一个定价
        ServerSkuBillingMethodResponseDTO created = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 更新定价
        updateDTO.setId(created.getId());
        ServerSkuBillingMethodResponseDTO updated = serverSkuBillingMethodService.updateServerSkuBillingMethod(updateDTO);
        
        assertNotNull(updated, "更新响应不应为空");
        assertEquals(created.getId(), updated.getId(), "ID应该保持不变");
        assertEquals(updateDTO.getUnitPrice(), updated.getUnitPrice(), "单价应该已更新");
        assertEquals(updateDTO.getRemark(), updated.getRemark(), "备注应该已更新");
        
        log.info("更新成功，新单价: {}", updated.getUnitPrice());
    }

    @Test
    void testUpdateServerSkuBillingMethod_NotFoundThrowsException() {
        log.info("测试更新主机套餐规格定价 - 记录不存在抛出异常");
        
        updateDTO.setId(999999L); // 不存在的ID
        
        PodSailException exception = assertThrows(PodSailException.class, () -> {
            serverSkuBillingMethodService.updateServerSkuBillingMethod(updateDTO);
        });
        
        log.info("捕获到预期异常: {}", exception.getMessage());
    }

    @Test
    void testGetServerSkuBillingMethodById_Success() {
        log.info("测试根据ID获取主机套餐规格定价 - 成功场景");
        
        // 先创建一个定价
        ServerSkuBillingMethodResponseDTO created = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 根据ID查询
        ServerSkuBillingMethodResponseDTO found = serverSkuBillingMethodService.getServerSkuBillingMethodById(created.getId());
        
        assertNotNull(found, "查询结果不应为空");
        assertEquals(created.getId(), found.getId(), "ID应该匹配");
        assertEquals(created.getServerSkuId(), found.getServerSkuId(), "服务器SKU ID应该匹配");
        assertEquals(created.getBillingCycle(), found.getBillingCycle(), "计费周期应该匹配");
        
        log.info("查询成功，找到记录: {}", found.getId());
    }

    @Test
    void testGetServerSkuBillingMethodById_NotFound() {
        log.info("测试根据ID获取主机套餐规格定价 - 记录不存在");
        
        ServerSkuBillingMethodResponseDTO found = serverSkuBillingMethodService.getServerSkuBillingMethodById(999999L);
        
        assertNull(found, "不存在的记录应该返回null");
        
        log.info("查询不存在的记录，正确返回null");
    }

    @Test
    void testGetServerSkuBillingMethodPage_Success() {
        log.info("测试分页查询主机套餐规格定价 - 成功场景");
        
        // 创建多个测试数据
        ServerSkuBillingMethodCreateDTO createDTO1 = new ServerSkuBillingMethodCreateDTO()
                .setServerSkuId("ecs.g1.c8m8")
                .setBillingCycle("1H")
                .setUnitPrice(new BigDecimal("10.0000"))
                .setSiteId(1)
                .setRemark("测试定价1");
        
        ServerSkuBillingMethodCreateDTO createDTO2 = new ServerSkuBillingMethodCreateDTO()
                .setServerSkuId("ecs.g1.c8m8")
                .setBillingCycle("1D")
                .setUnitPrice(new BigDecimal("200.0000"))
                .setSiteId(1)
                .setRemark("测试定价2");
        
        serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO1);
        serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO2);
        
        // 分页查询
        queryDTO.setServerSkuId("ecs.g1.c8m8");
        IPage<ServerSkuBillingMethodResponseDTO> page = serverSkuBillingMethodService.getServerSkuBillingMethodPage(queryDTO);
        
        assertNotNull(page, "分页结果不应为空");
        assertTrue(page.getTotal() >= 2, "总记录数应该至少为2");
        assertNotNull(page.getRecords(), "记录列表不应为空");
        assertTrue(page.getRecords().size() >= 2, "记录数应该至少为2");
        
        log.info("分页查询成功，总记录数: {}, 当前页记录数: {}", page.getTotal(), page.getRecords().size());
    }

    @Test
    void testGetServerSkuBillingMethodList_Success() {
        log.info("测试列表查询主机套餐规格定价 - 成功场景");
        
        // 创建测试数据
        serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 列表查询
        queryDTO.setServerSkuId(createDTO.getServerSkuId());
        List<ServerSkuBillingMethodResponseDTO> list = serverSkuBillingMethodService.getServerSkuBillingMethodList(queryDTO);
        
        assertNotNull(list, "查询结果不应为空");
        assertFalse(list.isEmpty(), "查询结果不应为空列表");
        assertEquals(1, list.size(), "应该查询到1条记录");
        
        ServerSkuBillingMethodResponseDTO found = list.get(0);
        assertEquals(createDTO.getServerSkuId(), found.getServerSkuId(), "服务器SKU ID应该匹配");
        
        log.info("列表查询成功，查询到记录数: {}", list.size());
    }

    @Test
    void testEnableServerSkuBillingMethod_Success() {
        log.info("测试启用主机套餐规格定价 - 成功场景");
        
        // 先创建一个定价
        ServerSkuBillingMethodResponseDTO created = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 启用定价
        boolean result = serverSkuBillingMethodService.enableServerSkuBillingMethod(created.getId());
        
        assertTrue(result, "启用操作应该成功");
        
        // 验证状态已更新
        ServerSkuBillingMethodResponseDTO updated = serverSkuBillingMethodService.getServerSkuBillingMethodById(created.getId());
        assertEquals(Integer.valueOf(1), updated.getStatus(), "状态应该为1（正常）");
        
        log.info("启用成功，当前状态: {}", updated.getStatus());
    }

    @Test
    void testDisableServerSkuBillingMethod_Success() {
        log.info("测试禁用主机套餐规格定价 - 成功场景");
        
        // 先创建一个定价
        ServerSkuBillingMethodResponseDTO created = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 禁用定价
        boolean result = serverSkuBillingMethodService.disableServerSkuBillingMethod(created.getId());
        
        assertTrue(result, "禁用操作应该成功");
        
        // 验证状态已更新
        ServerSkuBillingMethodResponseDTO updated = serverSkuBillingMethodService.getServerSkuBillingMethodById(created.getId());
        assertEquals(Integer.valueOf(2), updated.getStatus(), "状态应该为2（已失效）");
        
        log.info("禁用成功，当前状态: {}", updated.getStatus());
    }

    @Test
    void testExistsBillingMethod_Success() {
        log.info("测试检查定价组合是否存在 - 成功场景");
        
        // 先创建一个定价
        serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 检查存在的组合
        boolean exists = serverSkuBillingMethodService.existsBillingMethod(
                createDTO.getSiteId(), createDTO.getServerSkuId(), createDTO.getBillingCycle());
        
        assertTrue(exists, "已创建的定价组合应该存在");
        
        // 检查不存在的组合
        boolean notExists = serverSkuBillingMethodService.existsBillingMethod(
                createDTO.getSiteId(), createDTO.getServerSkuId(), "1D");
        
        assertFalse(notExists, "未创建的定价组合应该不存在");
        
        log.info("定价组合存在性检查成功");
    }

    @Test
    void testDeleteServerSkuBillingMethod_Success() {
        log.info("测试删除主机套餐规格定价 - 成功场景");
        
        // 先创建一个定价
        ServerSkuBillingMethodResponseDTO created = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        
        // 删除定价
        boolean result = serverSkuBillingMethodService.deleteServerSkuBillingMethod(created.getId());
        
        assertTrue(result, "删除操作应该成功");
        
        // 验证记录已删除
        ServerSkuBillingMethodResponseDTO deleted = serverSkuBillingMethodService.getServerSkuBillingMethodById(created.getId());
        assertNull(deleted, "删除后应该查询不到记录");
        
        log.info("删除成功，记录ID: {}", created.getId());
    }

    @Test
    void testDeleteServerSkuBillingMethods_Success() {
        log.info("测试批量删除主机套餐规格定价 - 成功场景");
        
        // 创建多个测试数据
        ServerSkuBillingMethodCreateDTO createDTO1 = new ServerSkuBillingMethodCreateDTO()
                .setServerSkuId("ecs.g1.c8m8")
                .setBillingCycle("1H")
                .setUnitPrice(new BigDecimal("10.0000"))
                .setSiteId(1)
                .setRemark("测试定价1");
        
        ServerSkuBillingMethodCreateDTO createDTO2 = new ServerSkuBillingMethodCreateDTO()
                .setServerSkuId("ecs.g1.c8m8")
                .setBillingCycle("1D")
                .setUnitPrice(new BigDecimal("200.0000"))
                .setSiteId(1)
                .setRemark("测试定价2");
        
        ServerSkuBillingMethodResponseDTO created1 = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO1);
        ServerSkuBillingMethodResponseDTO created2 = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO2);
        
        // 批量删除
        List<Long> ids = Arrays.asList(created1.getId(), created2.getId());
        boolean result = serverSkuBillingMethodService.deleteServerSkuBillingMethods(ids);
        
        assertTrue(result, "批量删除操作应该成功");
        
        // 验证记录已删除
        ServerSkuBillingMethodResponseDTO deleted1 = serverSkuBillingMethodService.getServerSkuBillingMethodById(created1.getId());
        ServerSkuBillingMethodResponseDTO deleted2 = serverSkuBillingMethodService.getServerSkuBillingMethodById(created2.getId());
        
        assertNull(deleted1, "删除后应该查询不到记录1");
        assertNull(deleted2, "删除后应该查询不到记录2");
        
        log.info("批量删除成功，删除记录数: {}", ids.size());
    }
}
