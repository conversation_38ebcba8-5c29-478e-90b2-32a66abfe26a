package com.cloudpod.podsail.controller.admin;

import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.domain.admin.form.AdminLoginForm;
import com.cloudpod.podsail.domain.admin.vo.AdminUserInfoVO;
import com.cloudpod.podsail.service.admin.AdminUserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @className UserController
 * @date 2025/8/19 21:30
 */
@RestController("adminUserController")
@RequestMapping("/admin")
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    @PostMapping("/login")
    public Response<AdminUserInfoVO> login(@Valid @RequestBody AdminLoginForm adminLoginForm) {
        return Response.success(adminUserService.login(adminLoginForm));
    }
}
