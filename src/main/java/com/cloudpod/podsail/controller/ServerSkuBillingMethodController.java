package com.cloudpod.podsail.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.service.billing.ServerSkuBillingMethodService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 主机套餐规格定价管理控制器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/server-sku-billing-methods")
@Api(tags = "主机套餐规格定价管理", description = "主机套餐规格定价相关的CRUD操作接口")
public class ServerSkuBillingMethodController {

    @Autowired
    private ServerSkuBillingMethodService serverSkuBillingMethodService;

    @PostMapping
    @ApiOperation(value = "创建主机套餐规格定价", notes = "创建新的主机套餐规格定价")
    public Response<ServerSkuBillingMethodResponseDTO> createServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价创建信息", required = true)
            @RequestBody @Valid ServerSkuBillingMethodCreateDTO createDTO) {
        
        log.info("创建主机套餐规格定价，请求参数: {}", createDTO);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.createServerSkuBillingMethod(createDTO);
        return Response.success(responseDTO);
    }

    @PutMapping
    @ApiOperation(value = "更新主机套餐规格定价", notes = "更新主机套餐规格定价信息")
    public Response<ServerSkuBillingMethodResponseDTO> updateServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价更新信息", required = true)
            @RequestBody @Valid ServerSkuBillingMethodUpdateDTO updateDTO) {
        
        log.info("更新主机套餐规格定价，请求参数: {}", updateDTO);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.updateServerSkuBillingMethod(updateDTO);
        return Response.success(responseDTO);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询主机套餐规格定价", notes = "根据主机套餐规格定价ID查询详细信息")
    public Response<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodById(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("根据ID查询主机套餐规格定价，ID: {}", id);
        ServerSkuBillingMethodResponseDTO responseDTO = serverSkuBillingMethodService.getServerSkuBillingMethodById(id);
        return Response.success(responseDTO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除主机套餐规格定价", notes = "根据主机套餐规格定价ID删除（逻辑删除）")
    public Response<Boolean> deleteServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("删除主机套餐规格定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.deleteServerSkuBillingMethod(id);
        return Response.success(result);
    }

    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除主机套餐规格定价", notes = "根据主机套餐规格定价ID列表批量删除（逻辑删除）")
    public Response<Boolean> deleteServerSkuBillingMethods(
            @ApiParam(value = "主机套餐规格定价ID列表", required = true)
            @RequestBody @Valid List<Long> ids) {
        
        log.info("批量删除主机套餐规格定价，ID列表: {}", ids);
        boolean result = serverSkuBillingMethodService.deleteServerSkuBillingMethods(ids);
        return Response.success(result);
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询主机套餐规格定价", notes = "根据条件分页查询主机套餐规格定价列表")
    public Response<IPage<ServerSkuBillingMethodResponseDTO>> getServerSkuBillingMethodPage(
            @ApiParam(value = "查询条件", required = true)
            @RequestBody @Valid ServerSkuBillingMethodQueryDTO queryDTO) {
        
        log.info("分页查询主机套餐规格定价，查询条件: {}", queryDTO);
        IPage<ServerSkuBillingMethodResponseDTO> page = serverSkuBillingMethodService.getServerSkuBillingMethodPage(queryDTO);
        return Response.success(page);
    }

  
    @PostMapping("/{id}/enable")
    @ApiOperation(value = "启用定价", notes = "启用指定的主机套餐规格定价")
    public Response<Boolean> enableServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("启用定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.enableServerSkuBillingMethod(id);
        return Response.success(result);
    }

    @PostMapping("/{id}/disable")
    @ApiOperation(value = "禁用定价", notes = "禁用指定的主机套餐规格定价")
    public Response<Boolean> disableServerSkuBillingMethod(
            @ApiParam(value = "主机套餐规格定价ID", required = true)
            @PathVariable("id") @NotNull Long id) {
        
        log.info("禁用定价，ID: {}", id);
        boolean result = serverSkuBillingMethodService.disableServerSkuBillingMethod(id);
        return Response.success(result);
    }

}
