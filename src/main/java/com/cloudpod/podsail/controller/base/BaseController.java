package com.cloudpod.podsail.controller.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.common.base.dto.BasePageQuery;
import com.cloudpod.podsail.common.base.response.Response;
import com.cloudpod.podsail.db.base.Entity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 基础Controller类
 * 提供通用的CRUD接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public class BaseController {


}
