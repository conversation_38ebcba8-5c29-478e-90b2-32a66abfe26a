package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户API密钥查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserApiKeyQueryDTO", description = "用户API密钥查询请求参数")
public class UserApiKeyQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "API密钥", example = "ak_1234567890abcdef")
    private String apiKey;

    @ApiModelProperty(value = "API密钥模糊查询", example = "ak_123")
    private String apiKeyLike;

    @ApiModelProperty(value = "备注", example = "开发环境使用")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "开发")
    private String remarkLike;

    @ApiModelProperty(value = "密钥状态", example = "1", notes = "1-正常 2-吊销")
    private Integer status;

    @ApiModelProperty(value = "最近访问时间开始", example = "1692345600000")
    private Long lastVisitTimeStart;

    @ApiModelProperty(value = "最近访问时间结束", example = "1692432000000")
    private Long lastVisitTimeEnd;
}
