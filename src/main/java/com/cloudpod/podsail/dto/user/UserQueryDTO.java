package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserQueryDTO", description = "用户查询请求参数")
public class UserQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名(邮箱)", example = "<EMAIL>")
    private String username;

    @ApiModelProperty(value = "用户名模糊查询", example = "user")
    private String usernameLike;

    @ApiModelProperty(value = "用户的唯一码", example = "USER001")
    private String code;

    @ApiModelProperty(value = "用户唯一码模糊查询", example = "USER")
    private String codeLike;

    @ApiModelProperty(value = "账户余额最小值", example = "0.0000")
    private BigDecimal balanceMin;

    @ApiModelProperty(value = "账户余额最大值", example = "1000.0000")
    private BigDecimal balanceMax;

    @ApiModelProperty(value = "注册时间开始", example = "1692345600000")
    private Long registerTimeStart;

    @ApiModelProperty(value = "注册时间结束", example = "1692432000000")
    private Long registerTimeEnd;
}
