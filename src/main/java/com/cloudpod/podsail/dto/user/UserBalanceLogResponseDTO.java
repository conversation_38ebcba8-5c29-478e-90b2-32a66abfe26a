package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户余额流水响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserBalanceLogResponseDTO", description = "用户余额流水响应数据")
public class UserBalanceLogResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "余额类型", example = "1", notes = "1-充值 2-消费 3-退款")
    private Integer type;

    @ApiModelProperty(value = "余额类型描述", example = "充值")
    private String typeDesc;

    @ApiModelProperty(value = "金额", example = "100.0000")
    private BigDecimal amount;

    @ApiModelProperty(value = "关联交易ID", example = "123")
    private Long tradeId;

    @ApiModelProperty(value = "备注", example = "账户充值")
    private String remark;

    /**
     * 获取类型描述
     */
    public String getTypeDesc() {
        if (type == null) {
            return "未知";
        }
        switch (type) {
            case 1:
                return "充值";
            case 2:
                return "消费";
            case 3:
                return "退款";
            default:
                return "未知";
        }
    }
}
