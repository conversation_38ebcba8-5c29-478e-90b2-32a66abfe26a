package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 用户余额流水创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserBalanceLogCreateDTO", description = "用户余额流水创建请求参数")
public class UserBalanceLogCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "余额类型", required = true, example = "1", notes = "1-充值 2-消费 3-退款")
    @NotNull(message = "余额类型不能为空")
    @Min(value = 1, message = "余额类型值必须在1-3之间")
    @Max(value = 3, message = "余额类型值必须在1-3之间")
    private Integer type;

    @ApiModelProperty(value = "金额", required = true, example = "100.0000")
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.0001", message = "金额必须大于0")
    @Digits(integer = 16, fraction = 4, message = "金额格式不正确，最多16位整数，4位小数")
    private BigDecimal amount;

    @ApiModelProperty(value = "关联交易ID", example = "123", notes = "充值payment_trade表，消费billing_record表，退款refund_order表")
    private Long tradeId;

    @ApiModelProperty(value = "备注", example = "账户充值")
    @Size(max = 64, message = "备注长度不能超过64个字符")
    private String remark;
}
