package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户API密钥响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserApiKeyResponseDTO", description = "用户API密钥响应数据")
public class UserApiKeyResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "API密钥", example = "ak_1234567890abcdef")
    private String apiKey;

    @ApiModelProperty(value = "备注", example = "开发环境使用")
    private String remark;

    @ApiModelProperty(value = "最近访问时间", example = "1692345600000")
    private Long lastVisitTime;

    @ApiModelProperty(value = "密钥状态", example = "1", notes = "1-正常 2-吊销")
    private Integer status;

    @ApiModelProperty(value = "密钥状态描述", example = "正常")
    private String statusDesc;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "吊销";
            default:
                return "未知";
        }
    }
}
