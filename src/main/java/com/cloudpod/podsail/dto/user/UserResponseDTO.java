package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserResponseDTO", description = "用户响应数据")
public class UserResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名(邮箱)", example = "<EMAIL>")
    private String username;

    @ApiModelProperty(value = "用户的唯一码", example = "USER001")
    private String code;

    @ApiModelProperty(value = "账户余额", example = "100.0000")
    private BigDecimal balance;

    @ApiModelProperty(value = "注册时间", example = "1692345600000")
    private Long registerTime;

    // 注意：密码字段不在响应DTO中返回，保护用户隐私
}
