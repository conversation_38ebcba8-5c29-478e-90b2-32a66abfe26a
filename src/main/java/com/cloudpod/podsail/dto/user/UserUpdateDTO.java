package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 用户更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserUpdateDTO", description = "用户更新请求参数")
public class UserUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户名(邮箱)", example = "<EMAIL>")
    @Email(message = "用户名必须是有效的邮箱格式")
    @Size(max = 128, message = "用户名长度不能超过128个字符")
    private String username;

    @ApiModelProperty(value = "密码", example = "newpassword123")
    @Size(min = 6, max = 256, message = "密码长度必须在6-256个字符之间")
    private String password;

    @ApiModelProperty(value = "用户的唯一码", example = "USER001")
    @Size(max = 32, message = "用户唯一码长度不能超过32个字符")
    private String code;

    @ApiModelProperty(value = "账户余额", example = "100.0000")
    private BigDecimal balance;

    @ApiModelProperty(value = "注册时间", example = "1692345600000")
    private Long registerTime;
}
