package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * 用户API密钥更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserApiKeyUpdateDTO", description = "用户API密钥更新请求参数")
public class UserApiKeyUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "备注", example = "生产环境使用")
    @Size(max = 64, message = "备注长度不能超过64个字符")
    private String remark;

    @ApiModelProperty(value = "密钥状态", example = "1", notes = "1-正常 2-吊销")
    @Min(value = 1, message = "密钥状态值必须为1或2")
    @Max(value = 2, message = "密钥状态值必须为1或2")
    private Integer status;

    @ApiModelProperty(value = "最近访问时间", example = "1692345600000")
    private Long lastVisitTime;

    // 用户ID和API密钥不允许修改
}
