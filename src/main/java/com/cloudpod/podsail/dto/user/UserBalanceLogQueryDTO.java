package com.cloudpod.podsail.dto.user;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户余额流水查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserBalanceLogQueryDTO", description = "用户余额流水查询请求参数")
public class UserBalanceLogQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "余额类型", example = "1", notes = "1-充值 2-消费 3-退款")
    private Integer type;

    @ApiModelProperty(value = "金额最小值", example = "0.0000")
    private BigDecimal amountMin;

    @ApiModelProperty(value = "金额最大值", example = "1000.0000")
    private BigDecimal amountMax;

    @ApiModelProperty(value = "关联交易ID", example = "123")
    private Long tradeId;

    @ApiModelProperty(value = "备注", example = "账户充值")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "充值")
    private String remarkLike;
}
