package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 主机套餐规格定价响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodResponseDTO", description = "主机套餐规格定价响应数据")
public class ServerSkuBillingMethodResponseDTO extends EntityDTO {

    @Serial private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "服务器套餐SKU ID", required = true, example = "ecs.g1.c8m8")
    @NotBlank()
    @Size(max = 64)
    private String serverSkuId;
    
    @ApiModelProperty(value = "计费周期", required = true, example = "1H", notes = "1H,1D,1M,3M,6M,1Y,2Y,3Y")
    @NotBlank()
    @Size(max = 16)
    private String billingCycle;
    
    @ApiModelProperty(value = "价格(元)", required = true, example = "10.0000")
    @NotNull()
    @DecimalMin(value = "0.01")
    @Digits(integer = 16, fraction = 4)
    private BigDecimal unitPrice;
    
    @ApiModelProperty(value = "站点ID", example = "1", notes = "1 国际站")
    private Integer siteId;
    
    @ApiModelProperty(value = "状态", example = "1", notes = "1-正常 2-已失效")
    @Min(value = 1)
    @Max(value = 2)
    private Integer status;
    
    @ApiModelProperty(value = "备注", example = "测试定价")
    @Size(max = 256)
    private String remark;
}
