package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 主机套餐规格定价查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodQueryDTO", description = "主机套餐规格定价查询请求参数")
public class ServerSkuBillingMethodQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "ecs.g1.c8m8")
    private String serverSkuId;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "价格最小值", example = "0.0000")
    private BigDecimal unitPriceMin;

    @ApiModelProperty(value = "价格最大值", example = "1000.0000")
    private BigDecimal unitPriceMax;
    
    @ApiModelProperty(value = "站点ID", example = "1", notes = "1-国际站")
    private Integer siteId;

    @ApiModelProperty(value = "状态", example = "1", notes = "1-正常 2-已失效")
    private Integer status;

    @ApiModelProperty(value = "备注", example = "测试定价")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "测试")
    private String remarkLike;
}
