package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * 计费记录更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BillingRecordUpdateDTO", description = "计费记录更新请求参数")
public class BillingRecordUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计费状态", example = "2", notes = "1-待扣费 2-已扣费 3-扣费失败")
    @Min(value = 1, message = "计费状态值必须在1-3之间")
    @Max(value = 3, message = "计费状态值必须在1-3之间")
    private Integer status;

    @ApiModelProperty(value = "扣费时间", example = "1692349200000")
    private Long chargeTime;

    @ApiModelProperty(value = "备注", example = "扣费成功")
    @Size(max = 256, message = "备注长度不能超过256个字符")
    private String remark;

    // 注意：用户ID、服务器实例ID、计费类型、计费周期、计费开始时间、
    // 计费结束时间、计费金额、货币单位等核心字段不允许修改
}
