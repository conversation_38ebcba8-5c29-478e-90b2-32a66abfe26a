package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 计费记录创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BillingRecordCreateDTO", description = "计费记录创建请求参数")
public class BillingRecordCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "服务器实例ID", required = true, example = "1")
    @NotNull(message = "服务器实例ID不能为空")
    private Long serverInstanceId;

    @ApiModelProperty(value = "计费类型", required = true, example = "1", notes = "1-按需付费 2-包年包月")
    @NotNull(message = "计费类型不能为空")
    @Min(value = 1, message = "计费类型值必须为1或2")
    @Max(value = 2, message = "计费类型值必须为1或2")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", required = true, example = "1H")
    @NotBlank(message = "计费周期不能为空")
    @Size(max = 16, message = "计费周期长度不能超过16个字符")
    private String billingCycle;

    @ApiModelProperty(value = "计费开始时间", required = true, example = "1692345600000")
    @NotNull(message = "计费开始时间不能为空")
    private Long startTime;

    @ApiModelProperty(value = "计费结束时间", required = true, example = "1692349200000")
    @NotNull(message = "计费结束时间不能为空")
    private Long endTime;

    @ApiModelProperty(value = "计费金额", required = true, example = "10.0000")
    @NotNull(message = "计费金额不能为空")
    @DecimalMin(value = "0.0001", message = "计费金额必须大于0")
    @Digits(integer = 16, fraction = 4, message = "计费金额格式不正确，最多16位整数，4位小数")
    private BigDecimal amount;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    @Size(max = 8, message = "货币单位长度不能超过8个字符")
    private String currency;

    @ApiModelProperty(value = "计费状态", example = "1", notes = "1-待扣费 2-已扣费 3-扣费失败")
    @Min(value = 1, message = "计费状态值必须在1-3之间")
    @Max(value = 3, message = "计费状态值必须在1-3之间")
    private Integer status;

    @ApiModelProperty(value = "扣费时间", example = "1692349200000")
    private Long chargeTime;

    @ApiModelProperty(value = "备注", example = "按需付费计费")
    @Size(max = 256, message = "备注长度不能超过256个字符")
    private String remark;
}
