package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 计费记录查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BillingRecordQueryDTO", description = "计费记录查询请求参数")
public class BillingRecordQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "服务器实例ID", example = "1")
    private Long serverInstanceId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费 2-包年包月")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "计费开始时间开始", example = "1692345600000")
    private Long startTimeStart;

    @ApiModelProperty(value = "计费开始时间结束", example = "1692432000000")
    private Long startTimeEnd;

    @ApiModelProperty(value = "计费结束时间开始", example = "1692345600000")
    private Long endTimeStart;

    @ApiModelProperty(value = "计费结束时间结束", example = "1692432000000")
    private Long endTimeEnd;

    @ApiModelProperty(value = "计费金额最小值", example = "0.0000")
    private BigDecimal amountMin;

    @ApiModelProperty(value = "计费金额最大值", example = "1000.0000")
    private BigDecimal amountMax;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "计费状态", example = "2", notes = "1-待扣费 2-已扣费 3-扣费失败")
    private Integer status;

    @ApiModelProperty(value = "扣费时间开始", example = "1692345600000")
    private Long chargeTimeStart;

    @ApiModelProperty(value = "扣费时间结束", example = "1692432000000")
    private Long chargeTimeEnd;

    @ApiModelProperty(value = "备注", example = "按需付费计费")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "按需")
    private String remarkLike;
}
