package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 主机套餐规格定价更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ServerSkuBillingMethodUpdateDTO", description = "主机套餐规格定价更新请求参数")
public class ServerSkuBillingMethodUpdateDTO extends BaseUpdateDTO {

    @Serial private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主机套餐规格定价ID", required = true)
    @NotNull
    private Long id;
    
    @ApiModelProperty(value = "价格(元)", required = true, example = "10.0000")
    @NotNull()
    @DecimalMin(value = "0.01")
    @Digits(integer = 16, fraction = 4)
    private BigDecimal unitPrice;
    
    @ApiModelProperty(value = "备注", example = "测试定价")
    @Size(max = 256)
    private String remark;
}
