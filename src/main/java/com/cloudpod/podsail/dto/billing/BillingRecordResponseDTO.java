package com.cloudpod.podsail.dto.billing;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 计费记录响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "BillingRecordResponseDTO", description = "计费记录响应数据")
public class BillingRecordResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "服务器实例ID", example = "1")
    private Long serverInstanceId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费 2-包年包月")
    private Integer billingType;

    @ApiModelProperty(value = "计费类型描述", example = "按需付费")
    private String billingTypeDesc;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "计费开始时间", example = "1692345600000")
    private Long startTime;

    @ApiModelProperty(value = "计费结束时间", example = "1692349200000")
    private Long endTime;

    @ApiModelProperty(value = "计费金额", example = "10.0000")
    private BigDecimal amount;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "计费状态", example = "2", notes = "1-待扣费 2-已扣费 3-扣费失败")
    private Integer status;

    @ApiModelProperty(value = "计费状态描述", example = "已扣费")
    private String statusDesc;

    @ApiModelProperty(value = "扣费时间", example = "1692349200000")
    private Long chargeTime;

    @ApiModelProperty(value = "备注", example = "按需付费计费")
    private String remark;

    /**
     * 获取计费类型描述
     */
    public String getBillingTypeDesc() {
        if (billingType == null) {
            return "未知";
        }
        switch (billingType) {
            case 1:
                return "按需付费";
            case 2:
                return "包年包月";
            default:
                return "未知";
        }
    }

    /**
     * 获取计费状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "待扣费";
            case 2:
                return "已扣费";
            case 3:
                return "扣费失败";
            default:
                return "未知";
        }
    }
}
