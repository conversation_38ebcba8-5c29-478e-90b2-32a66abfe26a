package com.cloudpod.podsail.dto.order;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 用户订单创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserOrderCreateDTO", description = "用户订单创建请求参数")
public class UserOrderCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "服务器套餐SKU ID", required = true, example = "sku-001")
    @NotBlank(message = "服务器套餐SKU ID不能为空")
    @Size(max = 64, message = "服务器套餐SKU ID长度不能超过64个字符")
    private String serverSkuId;

    @ApiModelProperty(value = "计费方式ID", required = true, example = "1")
    @NotNull(message = "计费方式ID不能为空")
    private Long billingMethodId;

    @ApiModelProperty(value = "计费类型", required = true, example = "1", notes = "1-按需付费 2-包年包月")
    @NotNull(message = "计费类型不能为空")
    @Min(value = 1, message = "计费类型值必须为1或2")
    @Max(value = 2, message = "计费类型值必须为1或2")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", required = true, example = "1H")
    @NotBlank(message = "计费周期不能为空")
    @Size(max = 16, message = "计费周期长度不能超过16个字符")
    private String billingCycle;

    @ApiModelProperty(value = "购买数量", example = "1")
    @Min(value = 1, message = "购买数量必须大于0")
    private Integer quantity;

    @ApiModelProperty(value = "单价", required = true, example = "10.0000")
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0.0001", message = "单价必须大于0")
    @Digits(integer = 16, fraction = 4, message = "单价格式不正确，最多16位整数，4位小数")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总金额", required = true, example = "10.0000")
    @NotNull(message = "总金额不能为空")
    @DecimalMin(value = "0.0001", message = "总金额必须大于0")
    @Digits(integer = 16, fraction = 4, message = "总金额格式不正确，最多16位整数，4位小数")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "优惠金额", example = "0.0000")
    @DecimalMin(value = "0.0000", message = "优惠金额不能小于0")
    @Digits(integer = 16, fraction = 4, message = "优惠金额格式不正确，最多16位整数，4位小数")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "实际支付金额", required = true, example = "10.0000")
    @NotNull(message = "实际支付金额不能为空")
    @DecimalMin(value = "0.0001", message = "实际支付金额必须大于0")
    @Digits(integer = 16, fraction = 4, message = "实际支付金额格式不正确，最多16位整数，4位小数")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    @Size(max = 8, message = "货币单位长度不能超过8个字符")
    private String currency;

    @ApiModelProperty(value = "订单过期时间", example = "1692345600000")
    private Long expireTime;

    @ApiModelProperty(value = "备注", example = "测试订单")
    @Size(max = 256, message = "备注长度不能超过256个字符")
    private String remark;

    // 订单号由系统自动生成
    // 订单状态和支付状态默认为待支付和未支付
}
