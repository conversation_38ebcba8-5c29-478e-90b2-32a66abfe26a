package com.cloudpod.podsail.dto.order;

import com.cloudpod.podsail.common.base.dto.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户订单响应DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserOrderResponseDTO", description = "用户订单响应数据")
public class UserOrderResponseDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "订单号", example = "ORDER20250819001")
    private String orderNo;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "sku-001")
    private String serverSkuId;

    @ApiModelProperty(value = "计费方式ID", example = "1")
    private Long billingMethodId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费 2-包年包月")
    private Integer billingType;

    @ApiModelProperty(value = "计费类型描述", example = "按需付费")
    private String billingTypeDesc;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "购买数量", example = "1")
    private Integer quantity;

    @ApiModelProperty(value = "单价", example = "10.0000")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总金额", example = "10.0000")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "优惠金额", example = "0.0000")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "实际支付金额", example = "10.0000")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "订单状态", example = "1", notes = "1-待支付 2-已支付 3-已取消 4-已退款")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态描述", example = "待支付")
    private String orderStatusDesc;

    @ApiModelProperty(value = "支付状态", example = "1", notes = "1-未支付 2-已支付 3-支付失败")
    private Integer payStatus;

    @ApiModelProperty(value = "支付状态描述", example = "未支付")
    private String payStatusDesc;

    @ApiModelProperty(value = "支付时间", example = "1692345600000")
    private Long payTime;

    @ApiModelProperty(value = "订单过期时间", example = "1692345600000")
    private Long expireTime;

    @ApiModelProperty(value = "备注", example = "测试订单")
    private String remark;

    /**
     * 获取计费类型描述
     */
    public String getBillingTypeDesc() {
        if (billingType == null) {
            return "未知";
        }
        switch (billingType) {
            case 1:
                return "按需付费";
            case 2:
                return "包年包月";
            default:
                return "未知";
        }
    }

    /**
     * 获取订单状态描述
     */
    public String getOrderStatusDesc() {
        if (orderStatus == null) {
            return "未知";
        }
        switch (orderStatus) {
            case 1:
                return "待支付";
            case 2:
                return "已支付";
            case 3:
                return "已取消";
            case 4:
                return "已退款";
            default:
                return "未知";
        }
    }

    /**
     * 获取支付状态描述
     */
    public String getPayStatusDesc() {
        if (payStatus == null) {
            return "未知";
        }
        switch (payStatus) {
            case 1:
                return "未支付";
            case 2:
                return "已支付";
            case 3:
                return "支付失败";
            default:
                return "未知";
        }
    }
}
