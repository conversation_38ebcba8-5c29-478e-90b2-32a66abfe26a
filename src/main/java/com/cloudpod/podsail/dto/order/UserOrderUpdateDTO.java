package com.cloudpod.podsail.dto.order;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 用户订单更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserOrderUpdateDTO", description = "用户订单更新请求参数")
public class UserOrderUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "优惠金额", example = "5.0000")
    @DecimalMin(value = "0.0000", message = "优惠金额不能小于0")
    @Digits(integer = 16, fraction = 4, message = "优惠金额格式不正确，最多16位整数，4位小数")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "实际支付金额", example = "95.0000")
    @DecimalMin(value = "0.0001", message = "实际支付金额必须大于0")
    @Digits(integer = 16, fraction = 4, message = "实际支付金额格式不正确，最多16位整数，4位小数")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "订单状态", example = "2", notes = "1-待支付 2-已支付 3-已取消 4-已退款")
    @Min(value = 1, message = "订单状态值必须在1-4之间")
    @Max(value = 4, message = "订单状态值必须在1-4之间")
    private Integer orderStatus;

    @ApiModelProperty(value = "支付状态", example = "2", notes = "1-未支付 2-已支付 3-支付失败")
    @Min(value = 1, message = "支付状态值必须在1-3之间")
    @Max(value = 3, message = "支付状态值必须在1-3之间")
    private Integer payStatus;

    @ApiModelProperty(value = "支付时间", example = "1692345600000")
    private Long payTime;

    @ApiModelProperty(value = "订单过期时间", example = "1692345600000")
    private Long expireTime;

    @ApiModelProperty(value = "备注", example = "订单已支付")
    @Size(max = 256, message = "备注长度不能超过256个字符")
    private String remark;

    // 注意：用户ID、订单号、服务器套餐SKU ID、计费方式ID、计费类型、计费周期、
    // 购买数量、单价、总金额、货币单位等核心字段不允许修改
}
