package com.cloudpod.podsail.dto.order;

import com.cloudpod.podsail.common.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户订单查询DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserOrderQueryDTO", description = "用户订单查询请求参数")
public class UserOrderQueryDTO extends BaseQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Long userId;

    @ApiModelProperty(value = "订单号", example = "ORDER20250819001")
    private String orderNo;

    @ApiModelProperty(value = "订单号模糊查询", example = "ORDER2025")
    private String orderNoLike;

    @ApiModelProperty(value = "服务器套餐SKU ID", example = "sku-001")
    private String serverSkuId;

    @ApiModelProperty(value = "服务器套餐SKU ID模糊查询", example = "sku")
    private String serverSkuIdLike;

    @ApiModelProperty(value = "计费方式ID", example = "1")
    private Long billingMethodId;

    @ApiModelProperty(value = "计费类型", example = "1", notes = "1-按需付费 2-包年包月")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", example = "1H")
    private String billingCycle;

    @ApiModelProperty(value = "购买数量最小值", example = "1")
    private Integer quantityMin;

    @ApiModelProperty(value = "购买数量最大值", example = "10")
    private Integer quantityMax;

    @ApiModelProperty(value = "单价最小值", example = "0.0000")
    private BigDecimal unitPriceMin;

    @ApiModelProperty(value = "单价最大值", example = "1000.0000")
    private BigDecimal unitPriceMax;

    @ApiModelProperty(value = "总金额最小值", example = "0.0000")
    private BigDecimal totalAmountMin;

    @ApiModelProperty(value = "总金额最大值", example = "10000.0000")
    private BigDecimal totalAmountMax;

    @ApiModelProperty(value = "实际支付金额最小值", example = "0.0000")
    private BigDecimal actualAmountMin;

    @ApiModelProperty(value = "实际支付金额最大值", example = "10000.0000")
    private BigDecimal actualAmountMax;

    @ApiModelProperty(value = "货币单位", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "订单状态", example = "1", notes = "1-待支付 2-已支付 3-已取消 4-已退款")
    private Integer orderStatus;

    @ApiModelProperty(value = "支付状态", example = "1", notes = "1-未支付 2-已支付 3-支付失败")
    private Integer payStatus;

    @ApiModelProperty(value = "支付时间开始", example = "1692345600000")
    private Long payTimeStart;

    @ApiModelProperty(value = "支付时间结束", example = "1692432000000")
    private Long payTimeEnd;

    @ApiModelProperty(value = "订单过期时间开始", example = "1692345600000")
    private Long expireTimeStart;

    @ApiModelProperty(value = "订单过期时间结束", example = "1692432000000")
    private Long expireTimeEnd;

    @ApiModelProperty(value = "备注", example = "测试订单")
    private String remark;

    @ApiModelProperty(value = "备注模糊查询", example = "测试")
    private String remarkLike;
}
