package com.cloudpod.podsail.dto.instance;

import com.cloudpod.podsail.common.base.dto.BaseUpdateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * 用户服务器实例更新DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserServerInstanceUpdateDTO", description = "用户服务器实例更新请求参数")
public class UserServerInstanceUpdateDTO extends BaseUpdateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器名称", example = "web-server-01-updated")
    @Size(max = 128, message = "服务器名称长度不能超过128个字符")
    private String serverName;

    @ApiModelProperty(value = "实例状态", example = "2", notes = "1-创建中 2-运行中 3-已停止 4-已销毁")
    @Min(value = 1, message = "实例状态值必须在1-4之间")
    @Max(value = 4, message = "实例状态值必须在1-4之间")
    private Integer instanceStatus;

    @ApiModelProperty(value = "结束计费时间", example = "1692432000000", notes = "0表示未结束")
    private Long endTime;

    @ApiModelProperty(value = "到期时间", example = "1692432000000", notes = "按需付费为0")
    private Long expireTime;

    @ApiModelProperty(value = "是否自动续费", example = "1", notes = "0-否 1-是")
    @Min(value = 0, message = "自动续费值必须为0或1")
    @Max(value = 1, message = "自动续费值必须为0或1")
    private Integer autoRenew;

    @ApiModelProperty(value = "可用区ID", example = "zone-002")
    @Size(max = 64, message = "可用区ID长度不能超过64个字符")
    private String zoneId;

    // 注意：用户ID、订单ID、服务器实例ID、服务器套餐SKU ID、计费类型、
    // 计费周期、开始计费时间、区域ID等核心字段不允许修改
}
