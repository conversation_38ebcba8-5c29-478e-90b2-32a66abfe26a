package com.cloudpod.podsail.dto.instance;

import com.cloudpod.podsail.common.base.dto.BaseCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

/**
 * 用户服务器实例创建DTO
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "UserServerInstanceCreateDTO", description = "用户服务器实例创建请求参数")
public class UserServerInstanceCreateDTO extends BaseCreateDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "订单ID", required = true, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty(value = "服务器实例ID(cloudpods)", required = true, example = "server-001")
    @NotBlank(message = "服务器实例ID不能为空")
    @Size(max = 64, message = "服务器实例ID长度不能超过64个字符")
    private String serverId;

    @ApiModelProperty(value = "服务器名称", required = true, example = "web-server-01")
    @NotBlank(message = "服务器名称不能为空")
    @Size(max = 128, message = "服务器名称长度不能超过128个字符")
    private String serverName;

    @ApiModelProperty(value = "服务器套餐SKU ID", required = true, example = "sku-001")
    @NotBlank(message = "服务器套餐SKU ID不能为空")
    @Size(max = 64, message = "服务器套餐SKU ID长度不能超过64个字符")
    private String serverSkuId;

    @ApiModelProperty(value = "计费类型", required = true, example = "1", notes = "1-按需付费 2-包年包月")
    @NotNull(message = "计费类型不能为空")
    @Min(value = 1, message = "计费类型值必须为1或2")
    @Max(value = 2, message = "计费类型值必须为1或2")
    private Integer billingType;

    @ApiModelProperty(value = "计费周期", required = true, example = "1H")
    @NotBlank(message = "计费周期不能为空")
    @Size(max = 16, message = "计费周期长度不能超过16个字符")
    private String billingCycle;

    @ApiModelProperty(value = "实例状态", example = "1", notes = "1-创建中 2-运行中 3-已停止 4-已销毁")
    @Min(value = 1, message = "实例状态值必须在1-4之间")
    @Max(value = 4, message = "实例状态值必须在1-4之间")
    private Integer instanceStatus;

    @ApiModelProperty(value = "开始计费时间", required = true, example = "1692345600000")
    @NotNull(message = "开始计费时间不能为空")
    private Long startTime;

    @ApiModelProperty(value = "结束计费时间", example = "1692432000000", notes = "0表示未结束")
    private Long endTime;

    @ApiModelProperty(value = "到期时间", example = "1692432000000", notes = "按需付费为0")
    private Long expireTime;

    @ApiModelProperty(value = "是否自动续费", example = "0", notes = "0-否 1-是")
    @Min(value = 0, message = "自动续费值必须为0或1")
    @Max(value = 1, message = "自动续费值必须为0或1")
    private Integer autoRenew;

    @ApiModelProperty(value = "区域ID", required = true, example = "region-001")
    @NotBlank(message = "区域ID不能为空")
    @Size(max = 64, message = "区域ID长度不能超过64个字符")
    private String regionId;

    @ApiModelProperty(value = "可用区ID", example = "zone-001")
    @Size(max = 64, message = "可用区ID长度不能超过64个字符")
    private String zoneId;

    @ApiModelProperty(value = "实例类型", example = "kvm", notes = "kvm(虚拟机) baremetal(裸机) - 必须明确指定或SKU中包含明确标识")
    @Size(max = 32, message = "实例类型长度不能超过32个字符")
    private String instanceType;
}
