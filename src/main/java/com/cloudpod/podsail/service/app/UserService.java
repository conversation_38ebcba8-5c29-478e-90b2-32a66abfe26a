package com.cloudpod.podsail.service.app;

import com.cloudpod.podsail.domain.app.form.*;
import com.cloudpod.podsail.domain.app.vo.AppUserInfoVO;

/**
 * <AUTHOR>
 * @className UserService
 * @date 2025/8/19 20:41
 */
public interface UserService {
    Boolean sendEmail(AppEmailForm appEmailForm);
    AppUserInfoVO register(AppUserRegisterForm appUserRegisterForm, Integer site);
    AppUserInfoVO passwordLogin(AppUserPasswordLoginForm appUserPasswordLoginForm);
    AppUserInfoVO verifyLogin(AppUserVerifyLoginForm appUserVerifyLoginForm);
    Boolean updatePassword(AppUserUpdatePasswordForm appUserUpdatePasswordForm);
    Boolean forgetPassword(AppUserForgetPasswordForm appUserForgetPasswordForm);
}
