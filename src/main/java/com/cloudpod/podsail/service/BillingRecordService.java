package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.dto.billing.BillingRecordCreateDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordQueryDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordResponseDTO;
import com.cloudpod.podsail.dto.billing.BillingRecordUpdateDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费记录服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface BillingRecordService {
    
    /**
     * 根据ID获取计费记录
     *
     * @param id 计费记录ID
     * @return 计费记录响应DTO
     */
    BillingRecordResponseDTO getBillingRecordById(Long id);
    
    /**
     * 分页查询计费记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<BillingRecordResponseDTO> getBillingRecordPage(BillingRecordQueryDTO queryDTO);
    
}
