package com.cloudpod.podsail.service.instance;

import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;

/**
 * 实例生命周期管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface InstanceLifecycleService {

    /**
     * 创建虚拟机实例
     * 这个方法会：
     * 1. 调用CloudPods创建虚拟机
     * 2. 将创建结果记录到本地数据库
     * 3. 开始计费流程
     *
     * @param createDTO 创建请求
     * @return 创建结果
     */
    UserServerInstanceResponseDTO createVirtualMachine(UserServerInstanceCreateDTO createDTO);

    /**
     * 启动虚拟机实例
     *
     * @param instanceId 本地实例ID
     * @return 操作结果
     */
    boolean startVirtualMachine(Long instanceId);

    /**
     * 停止虚拟机实例
     *
     * @param instanceId 本地实例ID
     * @param isForce 是否强制停止
     * @return 操作结果
     */
    boolean stopVirtualMachine(Long instanceId, boolean isForce);

    /**
     * 销毁虚拟机实例
     * 这个方法会：
     * 1. 调用CloudPods销毁虚拟机
     * 2. 结束计费流程
     * 3. 更新本地实例状态
     *
     * @param instanceId 本地实例ID
     * @param isForce 是否强制销毁
     * @return 操作结果
     */
    boolean destroyVirtualMachine(Long instanceId, boolean isForce);

    /**
     * 重启虚拟机实例
     *
     * @param instanceId 本地实例ID
     * @param isForce 是否强制重启
     * @return 操作结果
     */
    boolean rebootVirtualMachine(Long instanceId, boolean isForce);

    // 调整虚拟机配置功能已移除（本期不做）

    /**
     * 同步虚拟机状态
     * 从CloudPods获取最新状态并更新本地数据库
     *
     * @param instanceId 本地实例ID
     * @return 同步结果
     */
    boolean syncVirtualMachineStatus(Long instanceId);

    /**
     * 批量同步所有运行中的虚拟机状态
     * 定时任务调用此方法
     *
     * @return 同步的实例数量
     */
    int syncAllRunningVirtualMachineStatus();

    /**
     * 获取虚拟机的最新状态
     *
     * @param instanceId 本地实例ID
     * @return 虚拟机详情
     */
    UserServerInstanceResponseDTO getVirtualMachineDetail(Long instanceId);
}
