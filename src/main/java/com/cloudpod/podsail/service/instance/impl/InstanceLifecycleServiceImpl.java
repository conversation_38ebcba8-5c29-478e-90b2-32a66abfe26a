package com.cloudpod.podsail.service.instance.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserOrderDao;
import com.cloudpod.podsail.db.dao.UserServerInstanceDao;
import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.service.cloudpods.CloudPodsService;
import com.cloudpod.podsail.service.cloudpods.dto.*;
import com.cloudpod.podsail.service.billing.BillingEngineService;
import com.cloudpod.podsail.service.instance.InstanceLifecycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 实例生命周期管理服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class InstanceLifecycleServiceImpl implements InstanceLifecycleService {

    @Autowired
    private CloudPodsService cloudPodsService;

    @Autowired
    private UserServerInstanceDao userServerInstanceDao;

    @Autowired
    private UserOrderDao userOrderDao;

    @Autowired
    private BillingEngineService billingEngineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserServerInstanceResponseDTO createVirtualMachine(UserServerInstanceCreateDTO createDTO) {
        log.info("开始创建虚拟机实例: {}", createDTO);

        try {
            // 1. 验证订单状态
            UserOrder order = validateOrder(createDTO.getOrderId());

            // 2. 构建CloudPods创建请求
            CloudPodsVmCreateRequest cloudPodsRequest = buildCloudPodsCreateRequest(createDTO, order);

            // 3. 调用CloudPods创建虚拟机
            CloudPodsVmCreateResponse cloudPodsResponse = cloudPodsService.createVirtualMachine(cloudPodsRequest);

            if (!cloudPodsResponse.getSuccess()) {
                throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, 
                    "CloudPods创建虚拟机失败: " + cloudPodsResponse.getErrorMessage());
            }

            // 4. 创建本地实例记录
            UserServerInstance instance = createLocalInstance(createDTO, order, cloudPodsResponse);

            // 5. 更新订单状态为已交付
            updateOrderStatus(order.getId(), 5); // 已交付 (新增状态)

            // 6. 开始计费
            boolean billingStarted = billingEngineService.startBilling(instance.getId());
            if (!billingStarted) {
                log.warn("实例创建成功但启动计费失败: instanceId={}", instance.getId());
            } else {
                log.info("实例计费已启动: instanceId={}", instance.getId());
            }

            // 7. 转换为响应DTO
            UserServerInstanceResponseDTO responseDTO = BeanCopyUtil.copyProperties(instance, UserServerInstanceResponseDTO.class);
            
            log.info("虚拟机实例创建成功: instanceId={}, serverId={}", instance.getId(), instance.getServerId());
            return responseDTO;

        } catch (Exception e) {
            log.error("创建虚拟机实例失败", e);
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "创建虚拟机实例失败: " + e.getMessage());
        }
    }

    @Override
    public boolean startVirtualMachine(Long instanceId) {
        log.info("启动虚拟机实例: {}", instanceId);

        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            CloudPodsOperationResponse response = cloudPodsService.startVirtualMachine(instance.getServerId());
            
            if (response.getSuccess()) {
                // 更新本地状态为运行中
                updateInstanceStatus(instanceId, 2); // 运行中
                log.info("虚拟机实例启动成功: instanceId={}, serverId={}", instanceId, instance.getServerId());
                return true;
            } else {
                log.error("启动虚拟机实例失败: instanceId={}, error={}", instanceId, response.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("启动虚拟机实例异常: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    public boolean stopVirtualMachine(Long instanceId, boolean isForce) {
        log.info("停止虚拟机实例: instanceId={}, force={}", instanceId, isForce);

        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            CloudPodsOperationResponse response = cloudPodsService.stopVirtualMachine(instance.getServerId(), isForce);
            
            if (response.getSuccess()) {
                // 更新本地状态为已停止
                updateInstanceStatus(instanceId, 3); // 已停止
                log.info("虚拟机实例停止成功: instanceId={}, serverId={}", instanceId, instance.getServerId());
                return true;
            } else {
                log.error("停止虚拟机实例失败: instanceId={}, error={}", instanceId, response.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("停止虚拟机实例异常: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean destroyVirtualMachine(Long instanceId, boolean isForce) {
        log.info("销毁虚拟机实例: instanceId={}, force={}", instanceId, isForce);

        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            CloudPodsOperationResponse response = cloudPodsService.destroyVirtualMachine(instance.getServerId(), isForce);
            
            if (response.getSuccess()) {
                // 停止计费
                boolean billingStopped = billingEngineService.stopBilling(instanceId);
                if (!billingStopped) {
                    log.warn("虚拟机销毁成功但停止计费失败: instanceId={}", instanceId);
                }
                
                // 更新本地状态为已销毁，并设置结束时间
                long currentTime = System.currentTimeMillis();
                UserServerInstance updateInstance = new UserServerInstance();
                updateInstance.setId(instanceId);
                updateInstance.setInstanceStatus(4); // 4-已销毁
                updateInstance.setEndTime(currentTime);
                updateInstance.setUpdatedAt(currentTime);
                
                userServerInstanceDao.updateById(updateInstance);
                
                log.info("虚拟机实例销毁成功: instanceId={}, serverId={}", instanceId, instance.getServerId());
                return true;
            } else {
                log.error("销毁虚拟机实例失败: instanceId={}, error={}", instanceId, response.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("销毁虚拟机实例异常: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    public boolean rebootVirtualMachine(Long instanceId, boolean isForce) {
        log.info("重启虚拟机实例: instanceId={}, force={}", instanceId, isForce);

        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            CloudPodsOperationResponse response = cloudPodsService.rebootVirtualMachine(instance.getServerId(), isForce);
            
            if (response.getSuccess()) {
                log.info("虚拟机实例重启成功: instanceId={}, serverId={}", instanceId, instance.getServerId());
                return true;
            } else {
                log.error("重启虚拟机实例失败: instanceId={}, error={}", instanceId, response.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("重启虚拟机实例异常: instanceId={}", instanceId, e);
            return false;
        }
    }

    // changeVirtualMachineConfig 方法已删除（本期不做）

    @Override
    public boolean syncVirtualMachineStatus(Long instanceId) {
        log.info("同步虚拟机状态: instanceId={}", instanceId);

        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            CloudPodsVmDetailResponse response = cloudPodsService.getVirtualMachineDetail(instance.getServerId());
            
            if (response.getSuccess() && response.getServer() != null) {
                // 更新本地状态
                CloudPodsVmDetailResponse.ServerDetail serverDetail = response.getServer();
                updateInstanceFromCloudPods(instanceId, serverDetail);
                
                log.info("虚拟机状态同步成功: instanceId={}, status={}", instanceId, serverDetail.getStatus());
                return true;
            } else {
                log.error("同步虚拟机状态失败: instanceId={}, error={}", instanceId, response.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("同步虚拟机状态异常: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    public int syncAllRunningVirtualMachineStatus() {
        log.info("开始批量同步运行中的虚拟机状态");

        try {
            // 查询所有运行中的实例
            QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("instance_status", 1, 2); // 创建中、运行中
            queryWrapper.eq("is_del", 0);
            
            List<UserServerInstance> runningInstances = userServerInstanceDao.list(queryWrapper);
            
            int syncCount = 0;
            for (UserServerInstance instance : runningInstances) {
                try {
                    if (syncVirtualMachineStatus(instance.getId())) {
                        syncCount++;
                    }
                } catch (Exception e) {
                    log.error("同步实例状态失败: instanceId={}", instance.getId(), e);
                }
            }
            
            log.info("批量同步虚拟机状态完成: 总数={}, 成功={}", runningInstances.size(), syncCount);
            return syncCount;

        } catch (Exception e) {
            log.error("批量同步虚拟机状态异常", e);
            return 0;
        }
    }

    @Override
    public UserServerInstanceResponseDTO getVirtualMachineDetail(Long instanceId) {
        log.info("获取虚拟机详情: instanceId={}", instanceId);

        try {
            // 先同步最新状态
            syncVirtualMachineStatus(instanceId);
            
            // 获取本地实例信息
            UserServerInstance instance = getUserServerInstance(instanceId);
            
            return BeanCopyUtil.copyProperties(instance, UserServerInstanceResponseDTO.class);

        } catch (Exception e) {
            log.error("获取虚拟机详情失败: instanceId={}", instanceId, e);
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, "获取虚拟机详情失败: " + e.getMessage());
        }
    }

    // ================== 私有方法 ==================

    /**
     * 验证订单状态
     * 要求：订单必须已支付且未交付
     */
    private UserOrder validateOrder(Long orderId) {
        UserOrder order = userOrderDao.getById(orderId);
        if (order == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "订单不存在");
        }
        
        // 检查支付状态：必须已支付(2)
        if (!Integer.valueOf(2).equals(order.getPayStatus())) {
            throw new PodSailException(PodSailErrorCodeEnum.BUSINESS_ERROR, 
                "订单未支付，无法创建实例。当前支付状态: " + order.getPayStatus());
        }
        
        // 检查订单状态：必须是已支付(2)且未交付，不能是已交付(5)
        if (!Integer.valueOf(2).equals(order.getOrderStatus())) {
            String statusDesc = getOrderStatusDesc(order.getOrderStatus());
            throw new PodSailException(PodSailErrorCodeEnum.BUSINESS_ERROR, 
                "订单状态异常，无法创建实例。当前状态: " + statusDesc);
        }
        
        return order;
    }
    
    /**
     * 获取订单状态描述
     */
    private String getOrderStatusDesc(Integer orderStatus) {
        switch (orderStatus) {
            case 1: return "待支付";
            case 2: return "已支付";
            case 3: return "已取消";
            case 4: return "已退款";
            case 5: return "已交付";
            default: return "未知状态";
        }
    }

    /**
     * 构建CloudPods创建请求
     */
    private CloudPodsVmCreateRequest buildCloudPodsCreateRequest(UserServerInstanceCreateDTO createDTO, UserOrder order) {
        CloudPodsVmCreateRequest request = new CloudPodsVmCreateRequest();
        
        request.setAutoStart(true)
               .setGenerateName(createDTO.getServerName())
               .setDescription("PodSail创建的虚拟机实例")
               .setHypervisor(determineHypervisorType(createDTO, order))
               .setCount(1)
               .setOsArch("x86")
               .setSku(order.getServerSkuId())
               .setPassword("Aa123456789@") // 默认密码，后续可以配置化
               .setPreferRegion(createDTO.getRegionId())
               .setProjectId("system") // 使用默认项目，后续可以配置化
               .setPreferZone(createDTO.getZoneId())
               .setDuration(order.getBillingCycle())
               .setBillingType(Integer.valueOf(1).equals(order.getBillingType()) ? "postpaid" : "prepaid") // 1=按需付费，2=包年包月
               .setDeployTelegraf(true);

        // 添加系统盘配置（根据实例类型调整）
        List<CloudPodsVmCreateRequest.DiskConfig> disks = new ArrayList<>();
        String hypervisor = request.getHypervisor();
        CloudPodsVmCreateRequest.DiskConfig sysDisk = new CloudPodsVmCreateRequest.DiskConfig()
                .setDiskType("sys")
                .setIndex(0)
                .setBackend("baremetal".equals(hypervisor) ? "baremetal" : "local") // 裸机使用baremetal后端
                .setSize("baremetal".equals(hypervisor) ? 102400 : 30720) // 裸机默认100GB，虚拟机30GB
                .setImageId("a4efe102-0af4-431a-86ea-eac973e659bd") // 默认镜像ID，需要配置化
                .setMedium("baremetal".equals(hypervisor) ? "hybrid" : "ssd"); // 裸机使用hybrid，虚拟机使用ssd
        disks.add(sysDisk);
        request.setDisks(disks);

        // 添加默认网络配置
        List<CloudPodsVmCreateRequest.NetworkConfig> nets = new ArrayList<>();
        CloudPodsVmCreateRequest.NetworkConfig net = new CloudPodsVmCreateRequest.NetworkConfig()
                .setExit(false);
        nets.add(net);
        request.setNets(nets);

        return request;
    }

    /**
     * 确定Hypervisor类型
     * 必须由用户明确指定或从SKU中明确识别出来，不允许猜测
     */
    private String determineHypervisorType(UserServerInstanceCreateDTO createDTO, UserOrder order) {
        // 1. 如果用户明确指定了实例类型
        if (createDTO.getInstanceType() != null && !createDTO.getInstanceType().trim().isEmpty()) {
            String instanceType = createDTO.getInstanceType().toLowerCase().trim();
            if ("baremetal".equals(instanceType) || 
                "bare-metal".equals(instanceType) || 
                "physical".equals(instanceType)) {
                log.info("用户明确指定实例类型为裸机: {}", createDTO.getInstanceType());
                return "baremetal";
            } else if ("kvm".equals(instanceType) || 
                      "vm".equals(instanceType) || 
                      "virtual".equals(instanceType)) {
                log.info("用户明确指定实例类型为虚拟机: {}", createDTO.getInstanceType());
                return "kvm";
            } else {
                throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, 
                    "不支持的实例类型: " + createDTO.getInstanceType() + "，支持的类型: kvm(虚拟机), baremetal(裸机)");
            }
        }

        // 2. 如果SKU名称包含明确的实例类型关键词
        String skuId = order.getServerSkuId();
        if (skuId != null) {
            String lowerSkuId = skuId.toLowerCase();
            if (lowerSkuId.contains("baremetal") || 
                lowerSkuId.contains("bare") || 
                lowerSkuId.contains("physical") ||
                lowerSkuId.contains("bm-")) {
                log.info("根据SKU [{}] 自动识别为裸机实例", skuId);
                return "baremetal";
            } else if (lowerSkuId.contains("kvm") || 
                      lowerSkuId.contains("vm") || 
                      lowerSkuId.contains("virtual")) {
                log.info("根据SKU [{}] 自动识别为虚拟机实例", skuId);
                return "kvm";
            }
        }

        // 3. 无法确定实例类型，要求用户明确指定
        throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, 
            "无法确定实例类型，请明确指定 instanceType 参数 (kvm-虚拟机 或 baremetal-裸机)");
    }

    /**
     * 创建本地实例记录
     */
    private UserServerInstance createLocalInstance(UserServerInstanceCreateDTO createDTO, UserOrder order, CloudPodsVmCreateResponse cloudPodsResponse) {
        UserServerInstance instance = new UserServerInstance();
        
        long currentTime = System.currentTimeMillis();
        
        // 从CloudPods响应中获取服务器信息
        CloudPodsVmCreateResponse.ServerInfo serverInfo = cloudPodsResponse.getServers().get(0);
        
        instance.setUserId(createDTO.getUserId())
               .setOrderId(order.getId())
               .setServerId(serverInfo.getId())
               .setServerName(serverInfo.getName())
               .setServerSkuId(order.getServerSkuId())
               .setBillingType(order.getBillingType()) // Integer类型
               .setBillingCycle(order.getBillingCycle())
               .setInstanceStatus(1) // 1-创建中
               .setStartTime(currentTime)
               .setEndTime(0L)
               .setExpireTime(Integer.valueOf(2).equals(order.getBillingType()) ? calculateExpireTime(currentTime, order.getBillingCycle()) : 0L) // 2=包年包月需要到期时间，1=按需付费无到期时间
               .setAutoRenew(0) // 0-否
               .setRegionId(createDTO.getRegionId())
               .setZoneId(createDTO.getZoneId())
               .setCreatedAt(currentTime)
               .setUpdatedAt(currentTime)
               .setCreatedUid(createDTO.getUserId())
               .setUpdatedUid(createDTO.getUserId());

        userServerInstanceDao.save(instance);
        return instance;
    }

    /**
     * 计算到期时间
     */
    private Long calculateExpireTime(Long startTime, String billingCycle) {
        switch (billingCycle) {
            case "1M":
                return startTime + 30L * 24 * 60 * 60 * 1000; // 30天
            case "3M":
                return startTime + 90L * 24 * 60 * 60 * 1000; // 90天
            case "6M":
                return startTime + 180L * 24 * 60 * 60 * 1000; // 180天
            case "1Y":
                return startTime + 365L * 24 * 60 * 60 * 1000; // 365天
            case "2Y":
                return startTime + 730L * 24 * 60 * 60 * 1000; // 730天
            case "3Y":
                return startTime + 1095L * 24 * 60 * 60 * 1000; // 1095天
            default:
                return 0L;
        }
    }

    /**
     * 更新订单状态
     */
    private void updateOrderStatus(Long orderId, Integer status) {
        UserOrder updateOrder = new UserOrder();
        updateOrder.setId(orderId);
        updateOrder.setOrderStatus(status); // 使用传入的状态值
        updateOrder.setUpdatedAt(System.currentTimeMillis());
        
        userOrderDao.updateById(updateOrder);
    }

    /**
     * 获取用户服务器实例
     */
    private UserServerInstance getUserServerInstance(Long instanceId) {
        UserServerInstance instance = userServerInstanceDao.getById(instanceId);
        if (instance == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例不存在");
        }
        return instance;
    }

    /**
     * 更新实例状态
     */
    private void updateInstanceStatus(Long instanceId, Integer status) {
        UserServerInstance updateInstance = new UserServerInstance();
        updateInstance.setId(instanceId);
        updateInstance.setInstanceStatus(status); // 直接使用状态值
        updateInstance.setUpdatedAt(System.currentTimeMillis());
        
        userServerInstanceDao.updateById(updateInstance);
    }

    /**
     * 根据CloudPods数据更新本地实例
     */
    private void updateInstanceFromCloudPods(Long instanceId, CloudPodsVmDetailResponse.ServerDetail serverDetail) {
        UserServerInstance updateInstance = new UserServerInstance();
        updateInstance.setId(instanceId);
        updateInstance.setUpdatedAt(System.currentTimeMillis());
        
        // 根据CloudPods状态映射本地状态
        String cloudPodsStatus = serverDetail.getStatus();
        Integer localStatus = mapCloudPodsStatusToLocal(cloudPodsStatus);
        updateInstance.setInstanceStatus(localStatus);
        
        // 同步hypervisor信息到运行时字段
        if (serverDetail.getHypervisor() != null) {
            updateInstance.setHypervisor(serverDetail.getHypervisor());
            log.debug("同步实例类型: instanceId={}, hypervisor={}", instanceId, serverDetail.getHypervisor());
        }
        
        userServerInstanceDao.updateById(updateInstance);
    }

    /**
     * 将CloudPods状态映射为本地状态
     */
    private Integer mapCloudPodsStatusToLocal(String cloudPodsStatus) {
        switch (cloudPodsStatus.toLowerCase()) {
            case "running":
                return 2; // 运行中
            case "ready":
            case "stopped":
                return 3; // 已停止
            case "deploying":
            case "init":
                return 1; // 创建中
            case "deleting":
            case "deleted":
                return 4; // 已销毁
            default:
                return 1; // 默认创建中
        }
    }
}
