package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods虚拟机查询请求
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmQueryRequest {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 区域ID
     */
    private String cloudregionId;

    /**
     * 可用区ID
     */
    private String zoneId;

    /**
     * 虚拟化类型
     */
    private String hypervisor;

    /**
     * 状态
     */
    private String status;

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 分页大小
     */
    private Integer limit = 20;

    /**
     * 偏移量
     */
    private Integer offset = 0;

    /**
     * 排序字段
     */
    private String order = "created_at";

    /**
     * 排序方向 (asc/desc)
     */
    private String orderBy = "desc";

    /**
     * 过滤条件
     */
    private String filter;

    /**
     * 是否包含详情
     */
    private Boolean details = false;
}
