package com.cloudpod.podsail.service.cloudpods.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpod.podsail.service.cloudpods.CloudPodsService;
import com.cloudpod.podsail.service.cloudpods.dto.*;
import com.yunionyun.mcp.mcclient.AuthAgent;
import com.yunionyun.mcp.mcclient.Session;
import com.yunionyun.mcp.mcclient.EndpointType;
import com.yunionyun.mcp.mcclient.managers.ListResult;
import com.yunionyun.mcp.mcclient.managers.impl.compute.ServerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * CloudPods服务实现类 - 真实API调用
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class CloudPodsServiceImpl implements CloudPodsService {

    @Autowired
    private AuthAgent authAgent;

    @Override
    public CloudPodsVmCreateResponse createVirtualMachine(CloudPodsVmCreateRequest createRequest) {
        try {
            log.info("创建虚拟机请求: {}", createRequest);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            // 构建创建参数
            JSONObject createParams = buildCreateVmParams(createRequest);
            log.info("CloudPods创建虚拟机参数: {}", createParams.toJSONString());

            // 调用CloudPods API创建虚拟机
            JSONObject result = serverManager.Create(adminSession, createParams);
            log.info("CloudPods创建虚拟机响应: {}", result.toJSONString());

            return parseCreateVmResponse(result);

        } catch (Exception e) {
            log.error("创建虚拟机失败", e);
            return new CloudPodsVmCreateResponse()
                    .setSuccess(false)
                    .setErrorMessage("创建虚拟机失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsOperationResponse startVirtualMachine(String serverId) {
        try {
            log.info("启动虚拟机: {}", serverId);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            JSONObject params = new JSONObject();
            JSONObject result = serverManager.PerformAction(adminSession, serverId, "start", params);
            
            log.info("启动虚拟机响应: {}", result.toJSONString());
            return CloudPodsOperationResponse.success("start", result.toJSONString());

        } catch (Exception e) {
            log.error("启动虚拟机失败: {}", serverId, e);
            return CloudPodsOperationResponse.failure("start", "启动虚拟机失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsOperationResponse stopVirtualMachine(String serverId, boolean isForce) {
        try {
            log.info("停止虚拟机: {}, 强制: {}", serverId, isForce);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            JSONObject params = new JSONObject();
            params.put("is_force", isForce);
            
            JSONObject result = serverManager.PerformAction(adminSession, serverId, "stop", params);
            
            log.info("停止虚拟机响应: {}", result.toJSONString());
            return CloudPodsOperationResponse.success("stop", result.toJSONString());

        } catch (Exception e) {
            log.error("停止虚拟机失败: {}", serverId, e);
            return CloudPodsOperationResponse.failure("stop", "停止虚拟机失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsOperationResponse destroyVirtualMachine(String serverId, boolean isForce) {
        try {
            log.info("销毁虚拟机: {}, 强制: {}", serverId, isForce);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            JSONObject params = new JSONObject();
            params.put("is_force", isForce);
            
            JSONObject result = serverManager.PerformAction(adminSession, serverId, "delete", params);
            
            log.info("销毁虚拟机响应: {}", result.toJSONString());
            return CloudPodsOperationResponse.success("delete", result.toJSONString());

        } catch (Exception e) {
            log.error("销毁虚拟机失败: {}", serverId, e);
            return CloudPodsOperationResponse.failure("delete", "销毁虚拟机失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsOperationResponse rebootVirtualMachine(String serverId, boolean isForce) {
        try {
            log.info("重启虚拟机: {}, 强制: {}", serverId, isForce);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            JSONObject params = new JSONObject();
            params.put("is_force", isForce);
            
            JSONObject result = serverManager.PerformAction(adminSession, serverId, "restart", params);
            
            log.info("重启虚拟机响应: {}", result.toJSONString());
            return CloudPodsOperationResponse.success("restart", result.toJSONString());

        } catch (Exception e) {
            log.error("重启虚拟机失败: {}", serverId, e);
            return CloudPodsOperationResponse.failure("restart", "重启虚拟机失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsVmDetailResponse getVirtualMachineDetail(String serverId) {
        try {
            log.info("获取虚拟机详情: {}", serverId);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            JSONObject params = new JSONObject();
            params.put("details", true);
            
            JSONObject result = serverManager.Get(adminSession, serverId, params);
            log.info("获取虚拟机详情响应: {}", result.toJSONString());

            return parseVmDetailResponse(result);

        } catch (Exception e) {
            log.error("获取虚拟机详情失败: {}", serverId, e);
            return new CloudPodsVmDetailResponse()
                    .setSuccess(false)
                    .setErrorMessage("获取虚拟机详情失败: " + e.getMessage());
        }
    }

    @Override
    public CloudPodsVmListResponse getVirtualMachineList(CloudPodsVmQueryRequest queryRequest) {
        try {
            log.info("查询虚拟机列表: {}", queryRequest);
            
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            ServerManager serverManager = new ServerManager();

            // 构建查询参数
            JSONObject queryParams = buildVmQueryParams(queryRequest);
            log.info("CloudPods查询参数: {}", queryParams.toJSONString());

            ListResult listResult = serverManager.List(adminSession, queryParams);
            log.info("CloudPods查询虚拟机列表响应: 总数={}", listResult.getTotal());

            return parseVmListResponse(listResult);

        } catch (Exception e) {
            log.error("查询虚拟机列表失败", e);
            return new CloudPodsVmListResponse()
                    .setSuccess(false)
                    .setErrorMessage("查询虚拟机列表失败: " + e.getMessage());
        }
    }

    @Override
    public boolean checkConnection() {
        try {
            Session adminSession = authAgent.getAdminSession("region0", "zone0", EndpointType.ApigatewayURL);
            
            // 通过简单的服务器列表查询来检查连接
            ServerManager serverManager = new ServerManager();
            JSONObject params = new JSONObject();
            params.put("limit", 1);
            
            ListResult result = serverManager.List(adminSession, params);
            
            if (result != null) {
                log.info("CloudPods连接检查成功");
                return true;
            } else {
                log.error("CloudPods连接检查失败: 返回结果为空");
                return false;
            }
        } catch (Exception e) {
            log.error("CloudPods连接检查失败", e);
            return false;
        }
    }

    // ================== 私有方法 ==================

    /**
     * 构建虚拟机创建参数
     */
    private JSONObject buildCreateVmParams(CloudPodsVmCreateRequest request) {
        JSONObject params = new JSONObject();
        
        params.put("auto_start", request.getAutoStart());
        params.put("generate_name", request.getGenerateName());
        params.put("description", request.getDescription());
        params.put("hypervisor", request.getHypervisor());
        params.put("__count__", request.getCount());
        params.put("os_arch", request.getOsArch());
        params.put("sku", request.getSku());
        params.put("password", request.getPassword());
        params.put("prefer_region", request.getPreferRegion());
        params.put("project_id", request.getProjectId());
        params.put("prefer_zone", request.getPreferZone());
        params.put("bios", request.getBios());
        params.put("vdi", request.getVdi());
        params.put("vga", request.getVga());
        params.put("machine", request.getMachine());
        params.put("duration", request.getDuration());
        params.put("billing_type", request.getBillingType());
        params.put("deploy_telegraf", request.getDeployTelegraf());

        // 添加磁盘配置
        if (request.getDisks() != null && !request.getDisks().isEmpty()) {
            JSONArray disks = new JSONArray();
            for (CloudPodsVmCreateRequest.DiskConfig disk : request.getDisks()) {
                JSONObject diskObj = new JSONObject();
                diskObj.put("disk_type", disk.getDiskType());
                diskObj.put("index", disk.getIndex());
                diskObj.put("backend", disk.getBackend());
                diskObj.put("size", disk.getSize());
                if (disk.getImageId() != null) {
                    diskObj.put("image_id", disk.getImageId());
                }
                diskObj.put("medium", disk.getMedium());
                disks.add(diskObj);
            }
            params.put("disks", disks);
        }

        // 添加网络配置
        if (request.getNets() != null && !request.getNets().isEmpty()) {
            JSONArray nets = new JSONArray();
            for (CloudPodsVmCreateRequest.NetworkConfig net : request.getNets()) {
                JSONObject netObj = new JSONObject();
                netObj.put("exit", net.getExit());
                if (net.getNetworkId() != null) {
                    netObj.put("network_id", net.getNetworkId());
                }
                nets.add(netObj);
            }
            params.put("nets", nets);
        }

        return params;
    }

    /**
     * 解析创建虚拟机响应
     */
    private CloudPodsVmCreateResponse parseCreateVmResponse(JSONObject result) {
        CloudPodsVmCreateResponse response = new CloudPodsVmCreateResponse().setSuccess(true);
        
        // 解析服务器列表
        if (result.containsKey("data") && result.get("data") instanceof JSONArray) {
            JSONArray dataArray = result.getJSONArray("data");
            List<CloudPodsVmCreateResponse.ServerInfo> servers = new ArrayList<>();
            
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject serverObj = dataArray.getJSONObject(i);
                if (serverObj.containsKey("data")) {
                    JSONObject serverData = serverObj.getJSONObject("data");
                    CloudPodsVmCreateResponse.ServerInfo serverInfo = parseServerInfo(serverData);
                    servers.add(serverInfo);
                }
            }
            response.setServers(servers);
        }
        
        return response;
    }

    /**
     * 解析服务器信息
     */
    private CloudPodsVmCreateResponse.ServerInfo parseServerInfo(JSONObject serverData) {
        CloudPodsVmCreateResponse.ServerInfo serverInfo = new CloudPodsVmCreateResponse.ServerInfo();
        
        serverInfo.setId(serverData.getString("id"))
                  .setName(serverData.getString("name"))
                  .setStatus(serverData.getString("status"))
                  .setCreatedAt(serverData.getString("created_at"))
                  .setSku(serverData.getString("sku"))
                  .setMemory(serverData.getInteger("vmem_size"))
                  .setVcpus(serverData.getInteger("vcpu_count"))
                  .setDisk(serverData.getInteger("disk"))
                  .setCloudregionId(serverData.getString("cloudregion_id"))
                  .setZoneId(serverData.getString("zone_id"))
                  .setBillingType(serverData.getString("billing_type"))
                  .setBillingCycle(serverData.getString("billing_cycle"));
                  
        return serverInfo;
    }

    /**
     * 构建虚拟机查询参数
     */
    private JSONObject buildVmQueryParams(CloudPodsVmQueryRequest request) {
        JSONObject params = new JSONObject();
        
        if (request.getProjectId() != null) {
            params.put("project_id", request.getProjectId());
        }
        if (request.getCloudregionId() != null) {
            params.put("cloudregion_id", request.getCloudregionId());
        }
        if (request.getZoneId() != null) {
            params.put("zone_id", request.getZoneId());
        }
        if (request.getHypervisor() != null) {
            params.put("hypervisor", request.getHypervisor());
        }
        if (request.getStatus() != null) {
            params.put("status", request.getStatus());
        }
        if (request.getSearch() != null) {
            params.put("search", request.getSearch());
        }
        if (request.getFilter() != null) {
            params.put("filter", request.getFilter());
        }
        
        params.put("limit", request.getLimit());
        params.put("offset", request.getOffset());
        params.put("order", request.getOrder());
        params.put("order_by", request.getOrderBy());
        params.put("details", request.getDetails());
        
        return params;
    }

    /**
     * 解析虚拟机列表响应
     */
    private CloudPodsVmListResponse parseVmListResponse(ListResult listResult) {
        CloudPodsVmListResponse response = new CloudPodsVmListResponse()
                .setSuccess(true)
                .setTotal(listResult.getTotal())
                .setLimit(20) // 默认分页大小
                .setOffset(0); // 默认偏移量
        
        List<CloudPodsVmListResponse.ServerSummary> servers = new ArrayList<>();
        if (listResult.getData() != null) {
            for (Object obj : listResult.getData()) {
                JSONObject serverObj = (JSONObject) obj;
                CloudPodsVmListResponse.ServerSummary server = parseServerSummary(serverObj);
                servers.add(server);
            }
        }
        response.setServers(servers);
        
        return response;
    }

    /**
     * 解析服务器摘要信息
     */
    private CloudPodsVmListResponse.ServerSummary parseServerSummary(JSONObject serverObj) {
        CloudPodsVmListResponse.ServerSummary server = new CloudPodsVmListResponse.ServerSummary();
        
        server.setId(serverObj.getString("id"))
              .setName(serverObj.getString("name"))
              .setStatus(serverObj.getString("status"))
              .setSku(serverObj.getString("sku"))
              .setVmemSize(serverObj.getInteger("vmem_size"))
              .setVcpuCount(serverObj.getInteger("vcpu_count"))
              .setDisk(serverObj.getInteger("disk"))
              .setCloudregion(serverObj.getString("cloudregion"))
              .setZone(serverObj.getString("zone"))
              .setHost(serverObj.getString("host"))
              .setBillingType(serverObj.getString("billing_type"))
              .setBillingCycle(serverObj.getString("billing_cycle"))
              .setCreatedAt(serverObj.getString("created_at"))
              .setUpdatedAt(serverObj.getString("updated_at"))
              .setOsType(serverObj.getString("os_type"));

        // 解析IP地址
        List<String> ips = new ArrayList<>();
        if (serverObj.containsKey("ips") && serverObj.getString("ips") != null) {
            String ipsStr = serverObj.getString("ips");
            if (!ipsStr.isEmpty()) {
                String[] ipArray = ipsStr.split(",");
                for (String ip : ipArray) {
                    ips.add(ip.trim());
                }
            }
        }
        server.setIps(ips);
        
        return server;
    }

    /**
     * 解析虚拟机详情响应
     */
    private CloudPodsVmDetailResponse parseVmDetailResponse(JSONObject result) {
        CloudPodsVmDetailResponse response = new CloudPodsVmDetailResponse().setSuccess(true);
        
        CloudPodsVmDetailResponse.ServerDetail serverDetail = new CloudPodsVmDetailResponse.ServerDetail();
        
        serverDetail.setId(result.getString("id"))
                    .setName(result.getString("name"))
                    .setStatus(result.getString("status"))
                    .setDescription(result.getString("description"))
                    .setHypervisor(result.getString("hypervisor"))
                    .setSku(result.getString("sku"))
                    .setVmemSize(result.getInteger("vmem_size"))
                    .setVcpuCount(result.getInteger("vcpu_count"))
                    .setDisk(result.getInteger("disk"))
                    .setCloudregion(result.getString("cloudregion"))
                    .setCloudregionId(result.getString("cloudregion_id"))
                    .setZone(result.getString("zone"))
                    .setZoneId(result.getString("zone_id"))
                    .setHost(result.getString("host"))
                    .setHostId(result.getString("host_id"))
                    .setBillingType(result.getString("billing_type"))
                    .setBillingCycle(result.getString("billing_cycle"))
                    .setCreatedAt(result.getString("created_at"))
                    .setUpdatedAt(result.getString("updated_at"))
                    .setStartedAt(result.getString("started_at"))
                    .setAutoStart(result.getBoolean("auto_start"))
                    .setOsType(result.getString("os_type"))
                    .setOsDistribution(result.getString("os_distribution"))
                    .setOsVersion(result.getString("os_version"))
                    .setOsArch(result.getString("os_arch"));
        
        response.setServer(serverDetail);
        return response;
    }

    // buildConfigChangeParams 方法已删除（配置调整功能本期不做）

    // 已删除不必要的查询相关私有方法
}