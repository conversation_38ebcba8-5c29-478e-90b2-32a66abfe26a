package com.cloudpod.podsail.service.cloudpods;

import com.cloudpod.podsail.service.cloudpods.dto.*;

/**
 * CloudPods平台对接核心服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface CloudPodsService {

    /**
     * 创建虚拟机实例
     *
     * @param createRequest 创建请求参数
     * @return 创建结果
     */
    CloudPodsVmCreateResponse createVirtualMachine(CloudPodsVmCreateRequest createRequest);

    /**
     * 启动虚拟机实例
     *
     * @param serverId CloudPods服务器ID
     * @return 操作结果
     */
    CloudPodsOperationResponse startVirtualMachine(String serverId);

    /**
     * 停止虚拟机实例
     *
     * @param serverId CloudPods服务器ID
     * @param isForce 是否强制停止
     * @return 操作结果
     */
    CloudPodsOperationResponse stopVirtualMachine(String serverId, boolean isForce);

    /**
     * 销毁虚拟机实例
     *
     * @param serverId CloudPods服务器ID
     * @param isForce 是否强制销毁
     * @return 操作结果
     */
    CloudPodsOperationResponse destroyVirtualMachine(String serverId, boolean isForce);

    /**
     * 重启虚拟机实例
     *
     * @param serverId CloudPods服务器ID
     * @param isForce 是否强制重启
     * @return 操作结果
     */
    CloudPodsOperationResponse rebootVirtualMachine(String serverId, boolean isForce);

    /**
     * 获取虚拟机详细信息
     *
     * @param serverId CloudPods服务器ID
     * @return 虚拟机详情
     */
    CloudPodsVmDetailResponse getVirtualMachineDetail(String serverId);

    /**
     * 获取虚拟机列表
     *
     * @param queryRequest 查询参数
     * @return 虚拟机列表
     */
    CloudPodsVmListResponse getVirtualMachineList(CloudPodsVmQueryRequest queryRequest);

    /**
     * 检查CloudPods连接状态
     *
     * @return 连接状态
     */
    boolean checkConnection();
}
