package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods可用区查询请求
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsZoneQueryRequest {

    /**
     * 区域ID
     */
    private String cloudregionId;

    /**
     * 是否可用
     */
    private Boolean usable = true;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 分页大小
     */
    private Integer limit = 50;

    /**
     * 偏移量
     */
    private Integer offset = 0;
}
