package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods虚拟机配置调整请求
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmConfigChangeRequest {

    /**
     * 新SKU规格
     */
    private String sku;

    /**
     * 是否自动启动
     */
    private Boolean autoStart = true;

    /**
     * 新增磁盘列表
     */
    private List<DiskConfig> disks;

    /**
     * 磁盘配置
     */
    @Data
    @Accessors(chain = true)
    public static class DiskConfig {
        /**
         * 磁盘类型 (data)
         */
        private String diskType;

        /**
         * 索引
         */
        private Integer index;

        /**
         * 大小(MB)
         */
        private Integer size;

        /**
         * 后端存储
         */
        private String backend;

        /**
         * 存储介质 (ssd/hdd)
         */
        private String medium;
    }
}
