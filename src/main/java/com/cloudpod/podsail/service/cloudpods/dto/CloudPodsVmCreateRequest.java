package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods虚拟机创建请求
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmCreateRequest {

    /**
     * 自动启动
     */
    private Boolean autoStart = true;

    /**
     * 生成名称前缀
     */
    private String generateName;

    /**
     * 描述
     */
    private String description = "";

    /**
     * 虚拟化类型 (kvm/baremetal)
     */
    private String hypervisor = "kvm";

    /**
     * 创建数量
     */
    private Integer count = 1;

    /**
     * 磁盘配置
     */
    private List<DiskConfig> disks;

    /**
     * 网络配置
     */
    private List<NetworkConfig> nets;

    /**
     * 首选区域
     */
    private String preferRegion;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 操作系统架构
     */
    private String osArch = "x86";

    /**
     * SKU规格
     */
    private String sku;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 首选可用区
     */
    private String preferZone;

    /**
     * BIOS类型
     */
    private String bios = "";

    /**
     * VDI协议
     */
    private String vdi = "";

    /**
     * VGA类型
     */
    private String vga = "";

    /**
     * 机器类型
     */
    private String machine = "pc";

    /**
     * 计费时长
     */
    private String duration = "1h";

    /**
     * 计费类型 (postpaid/prepaid)
     */
    private String billingType = "postpaid";

    /**
     * 是否部署监控
     */
    private Boolean deployTelegraf = true;

    /**
     * 磁盘配置
     */
    @Data
    @Accessors(chain = true)
    public static class DiskConfig {
        /**
         * 磁盘类型 (sys/data)
         */
        private String diskType;

        /**
         * 索引
         */
        private Integer index;

        /**
         * 后端存储
         */
        private String backend;

        /**
         * 大小(MB)
         */
        private Integer size;

        /**
         * 镜像ID (系统盘必需)
         */
        private String imageId;

        /**
         * 存储介质 (ssd/hdd)
         */
        private String medium;
    }

    /**
     * 网络配置
     */
    @Data
    @Accessors(chain = true)
    public static class NetworkConfig {
        /**
         * 是否出网
         */
        private Boolean exit = false;

        /**
         * 网络ID
         */
        private String networkId;
    }
}
