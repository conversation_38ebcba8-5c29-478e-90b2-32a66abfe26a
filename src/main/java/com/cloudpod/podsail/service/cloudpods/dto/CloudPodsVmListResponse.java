package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods虚拟机列表响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmListResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 服务器列表
     */
    private List<ServerSummary> servers;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 分页大小
     */
    private Integer limit;

    /**
     * 偏移量
     */
    private Integer offset;

    /**
     * 服务器摘要信息
     */
    @Data
    @Accessors(chain = true)
    public static class ServerSummary {
        /**
         * 服务器ID
         */
        private String id;

        /**
         * 服务器名称
         */
        private String name;

        /**
         * 状态
         */
        private String status;

        /**
         * SKU规格
         */
        private String sku;

        /**
         * 内存大小(MB)
         */
        private Integer vmemSize;

        /**
         * CPU核数
         */
        private Integer vcpuCount;

        /**
         * 磁盘大小(MB)
         */
        private Integer disk;

        /**
         * 区域
         */
        private String cloudregion;

        /**
         * 可用区
         */
        private String zone;

        /**
         * 宿主机
         */
        private String host;

        /**
         * 计费类型
         */
        private String billingType;

        /**
         * 计费周期
         */
        private String billingCycle;

        /**
         * 创建时间
         */
        private String createdAt;

        /**
         * 更新时间
         */
        private String updatedAt;

        /**
         * IP地址列表
         */
        private List<String> ips;

        /**
         * 操作系统类型
         */
        private String osType;
    }
}
