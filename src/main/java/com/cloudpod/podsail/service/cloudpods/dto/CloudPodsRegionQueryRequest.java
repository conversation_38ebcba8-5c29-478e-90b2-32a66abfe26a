package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods区域查询请求
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsRegionQueryRequest {

    /**
     * 云环境类型
     */
    private String cloudEnv = "onpremise";

    /**
     * 是否可用
     */
    private Boolean usable = true;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 是否显示模拟
     */
    private Boolean showEmulated = true;

    /**
     * 作用域
     */
    private String scope = "project";

    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 分页大小
     */
    private Integer limit = 50;

    /**
     * 偏移量
     */
    private Integer offset = 0;
}
