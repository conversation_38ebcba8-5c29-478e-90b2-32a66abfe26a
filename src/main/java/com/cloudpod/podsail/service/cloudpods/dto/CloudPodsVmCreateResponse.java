package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods虚拟机创建响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmCreateResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 创建的服务器列表
     */
    private List<ServerInfo> servers;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 服务器信息
     */
    @Data
    @Accessors(chain = true)
    public static class ServerInfo {
        /**
         * 服务器ID
         */
        private String id;

        /**
         * 服务器名称
         */
        private String name;

        /**
         * 状态
         */
        private String status;

        /**
         * 创建时间
         */
        private String createdAt;

        /**
         * SKU规格
         */
        private String sku;

        /**
         * 内存大小(MB)
         */
        private Integer memory;

        /**
         * CPU核数
         */
        private Integer vcpus;

        /**
         * 磁盘大小(MB)
         */
        private Integer disk;

        /**
         * 区域ID
         */
        private String cloudregionId;

        /**
         * 可用区ID
         */
        private String zoneId;

        /**
         * 计费类型
         */
        private String billingType;

        /**
         * 计费周期
         */
        private String billingCycle;
    }
}
