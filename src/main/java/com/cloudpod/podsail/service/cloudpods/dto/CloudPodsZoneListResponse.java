package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods可用区列表响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsZoneListResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 可用区列表
     */
    private List<ZoneInfo> zones;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 可用区信息
     */
    @Data
    @Accessors(chain = true)
    public static class ZoneInfo {
        /**
         * 可用区ID
         */
        private String id;

        /**
         * 可用区名称
         */
        private String name;

        /**
         * 区域ID
         */
        private String cloudregionId;

        /**
         * 区域名称
         */
        private String cloudregion;

        /**
         * 状态
         */
        private String status;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 描述
         */
        private String description;

        /**
         * 宿主机数量
         */
        private Integer hostCount;

        /**
         * 虚拟机数量
         */
        private Integer guestCount;

        /**
         * 容量信息
         */
        private String capacity;
    }
}
