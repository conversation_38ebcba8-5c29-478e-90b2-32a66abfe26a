package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * CloudPods虚拟机详情响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsVmDetailResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 服务器详情
     */
    private ServerDetail server;

    /**
     * 服务器详情
     */
    @Data
    @Accessors(chain = true)
    public static class ServerDetail {
        /**
         * 服务器ID
         */
        private String id;

        /**
         * 服务器名称
         */
        private String name;

        /**
         * 状态
         */
        private String status;

        /**
         * 描述
         */
        private String description;

        /**
         * 虚拟化类型
         */
        private String hypervisor;

        /**
         * SKU规格
         */
        private String sku;

        /**
         * 内存大小(MB)
         */
        private Integer vmemSize;

        /**
         * CPU核数
         */
        private Integer vcpuCount;

        /**
         * 磁盘大小(MB)
         */
        private Integer disk;

        /**
         * 区域
         */
        private String cloudregion;

        /**
         * 区域ID
         */
        private String cloudregionId;

        /**
         * 可用区
         */
        private String zone;

        /**
         * 可用区ID
         */
        private String zoneId;

        /**
         * 宿主机
         */
        private String host;

        /**
         * 宿主机ID
         */
        private String hostId;

        /**
         * 计费类型
         */
        private String billingType;

        /**
         * 计费周期
         */
        private String billingCycle;

        /**
         * 创建时间
         */
        private String createdAt;

        /**
         * 更新时间
         */
        private String updatedAt;

        /**
         * 启动时间
         */
        private String startedAt;

        /**
         * 是否自动启动
         */
        private Boolean autoStart;

        /**
         * 操作系统
         */
        private String osType;

        /**
         * 操作系统分布
         */
        private String osDistribution;

        /**
         * 操作系统版本
         */
        private String osVersion;

        /**
         * 操作系统架构
         */
        private String osArch;

        /**
         * 网络信息
         */
        private List<NetworkInfo> networks;

        /**
         * 磁盘信息
         */
        private List<DiskInfo> disks;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 网络信息
     */
    @Data
    @Accessors(chain = true)
    public static class NetworkInfo {
        private String mac;
        private String ipAddr;
        private String ip6Addr;
        private String networkId;
        private String networkName;
        private String wireId;
        private String wireName;
    }

    /**
     * 磁盘信息
     */
    @Data
    @Accessors(chain = true)
    public static class DiskInfo {
        private String id;
        private String name;
        private String diskType;
        private Integer diskSize;
        private String backend;
        private String medium;
        private String imageId;
        private String imageName;
    }
}
