package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudPods操作响应通用类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsOperationResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 操作结果数据
     */
    private Object data;

    /**
     * HTTP状态码
     */
    private Integer httpStatus;

    /**
     * 操作类型
     */
    private String operationType;

    public static CloudPodsOperationResponse success(String operationType, Object data) {
        return new CloudPodsOperationResponse()
                .setSuccess(true)
                .setOperationType(operationType)
                .setData(data);
    }

    public static CloudPodsOperationResponse failure(String operationType, String errorMessage) {
        return new CloudPodsOperationResponse()
                .setSuccess(false)
                .setOperationType(operationType)
                .setErrorMessage(errorMessage);
    }

    public static CloudPodsOperationResponse failure(String operationType, String errorMessage, Integer httpStatus) {
        return new CloudPodsOperationResponse()
                .setSuccess(false)
                .setOperationType(operationType)
                .setErrorMessage(errorMessage)
                .setHttpStatus(httpStatus);
    }
}
