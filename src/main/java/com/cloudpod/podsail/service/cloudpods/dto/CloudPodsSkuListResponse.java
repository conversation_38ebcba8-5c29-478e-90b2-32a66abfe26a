package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods SKU列表响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsSkuListResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * SKU列表
     */
    private List<SkuInfo> skus;

    /**
     * 总数
     */
    private Integer total;

    /**
     * SKU信息
     */
    @Data
    @Accessors(chain = true)
    public static class SkuInfo {
        /**
         * SKU ID
         */
        private String id;

        /**
         * SKU名称
         */
        private String name;

        /**
         * CPU核数
         */
        private Integer cpuCoreCount;

        /**
         * 内存大小(MB)
         */
        private Integer memoryMb;

        /**
         * 实例类型系列
         */
        private String instanceTypeFamily;

        /**
         * 实例类型分类
         */
        private String instanceTypeCategory;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 创建时间
         */
        private String createdAt;

        /**
         * 更新时间
         */
        private String updatedAt;

        /**
         * 区域ID
         */
        private String cloudregionId;

        /**
         * 可用区ID
         */
        private String zoneId;

        /**
         * 提供商
         */
        private String provider;

        /**
         * 品牌
         */
        private String brand;

        /**
         * CPU架构
         */
        private String cpuArch;

        /**
         * 操作系统类型
         */
        private String osName;
    }
}
