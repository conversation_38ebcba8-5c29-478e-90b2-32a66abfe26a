package com.cloudpod.podsail.service.cloudpods.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CloudPods区域列表响应
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@Accessors(chain = true)
public class CloudPodsRegionListResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 区域列表
     */
    private List<RegionInfo> regions;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 区域信息
     */
    @Data
    @Accessors(chain = true)
    public static class RegionInfo {
        /**
         * 区域ID
         */
        private String id;

        /**
         * 区域名称
         */
        private String name;

        /**
         * 状态
         */
        private String status;

        /**
         * 是否启用
         */
        private Boolean enabled;

        /**
         * 描述
         */
        private String description;

        /**
         * 提供商
         */
        private String provider;

        /**
         * 品牌
         */
        private String brand;

        /**
         * 云环境
         */
        private String cloudEnv;

        /**
         * 可用区数量
         */
        private Integer zoneCount;

        /**
         * VPC数量
         */
        private Integer vpcCount;

        /**
         * 网络数量
         */
        private Integer networkCount;
    }
}
