package com.cloudpod.podsail.service.scheduled;

import com.cloudpod.podsail.service.billing.BillingEngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 计费定时任务调度器
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Component
public class BillingScheduler {

    @Autowired
    private BillingEngineService billingEngineService;

    /**
     * 按分钟计费任务
     * 每分钟执行一次，处理按分钟计费的实例
     */
    @Scheduled(fixedRate = 60000) // 每60秒执行一次
    public void executeMinutelyBilling() {
        try {
            log.debug("开始执行按分钟计费任务");
            int processedCount = billingEngineService.executePeriodicBilling();
            if (processedCount > 0) {
                log.info("按分钟计费任务完成: 处理实例数={}", processedCount);
            }
        } catch (Exception e) {
            log.error("按分钟计费任务执行失败", e);
        }
    }

    /**
     * 余额检查任务
     * 每5分钟执行一次，检查余额不足的实例并自动销毁
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void checkInsufficientBalance() {
        try {
            log.debug("开始执行余额检查任务");
            int destroyedCount = billingEngineService.checkAndHandleInsufficientBalance();
            if (destroyedCount > 0) {
                log.warn("余额检查任务完成: 销毁实例数={}", destroyedCount);
            }
        } catch (Exception e) {
            log.error("余额检查任务执行失败", e);
        }
    }

    /**
     * 包年包月到期检查任务
     * 每小时执行一次，检查到期的包年包月实例并自动销毁
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void checkExpiredInstances() {
        try {
            log.debug("开始执行到期检查任务");
            int destroyedCount = billingEngineService.checkAndHandleExpiredInstances();
            if (destroyedCount > 0) {
                log.warn("到期检查任务完成: 销毁实例数={}", destroyedCount);
            }
        } catch (Exception e) {
            log.error("到期检查任务执行失败", e);
        }
    }

    /**
     * 计费统计任务
     * 每天凌晨2点执行，生成计费统计报告
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void generateBillingStatistics() {
        try {
            log.info("开始生成计费统计报告");
            // TODO: 实现计费统计逻辑
            log.info("计费统计报告生成完成");
        } catch (Exception e) {
            log.error("生成计费统计报告失败", e);
        }
    }
}
