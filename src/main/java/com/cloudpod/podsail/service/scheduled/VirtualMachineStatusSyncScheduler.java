package com.cloudpod.podsail.service.scheduled;

import com.cloudpod.podsail.service.instance.InstanceLifecycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 虚拟机状态同步定时任务
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Component
public class VirtualMachineStatusSyncScheduler {

    @Autowired
    private InstanceLifecycleService instanceLifecycleService;

    /**
     * 每5分钟同步一次虚拟机状态
     * 确保本地状态与CloudPods保持一致
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void syncVirtualMachineStatus() {
        try {
            log.info("开始定时同步虚拟机状态");
            
            int syncCount = instanceLifecycleService.syncAllRunningVirtualMachineStatus();
            
            log.info("定时同步虚拟机状态完成，同步数量: {}", syncCount);
            
        } catch (Exception e) {
            log.error("定时同步虚拟机状态失败", e);
        }
    }

    /**
     * 每小时检查一次CloudPods连接状态
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时
    public void checkCloudPodsConnection() {
        try {
            log.info("开始检查CloudPods连接状态");
            
            // 这里可以扩展为连接健康检查
            log.info("CloudPods连接状态检查完成");
            
        } catch (Exception e) {
            log.error("CloudPods连接状态检查失败", e);
        }
    }
}
