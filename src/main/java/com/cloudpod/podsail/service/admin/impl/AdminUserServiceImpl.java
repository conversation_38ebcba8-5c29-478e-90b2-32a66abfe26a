package com.cloudpod.podsail.service.admin.impl;

import cn.hutool.crypto.digest.BCrypt;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.config.PasswordConfig;
import com.cloudpod.podsail.db.dao.AdminUserDao;
import com.cloudpod.podsail.db.entity.AdminUser;
import com.cloudpod.podsail.domain.admin.form.AdminLoginForm;
import com.cloudpod.podsail.domain.admin.vo.AdminUserInfoVO;
import com.cloudpod.podsail.service.admin.AdminUserService;
import com.cloudpod.podsail.service.auth.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @className AdminUserServiceImpl
 * @date 2025/8/19 20:40
 */
@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired
    private AdminUserDao adminUserDao;
    @Autowired
    private PasswordConfig passwordConfig;
    @Autowired
    private TokenService tokenService;

    @Override
    public AdminUserInfoVO login(AdminLoginForm adminLoginForm) {
        Optional<AdminUser> adminUserOptional = adminUserDao.lambdaQuery()
                .eq(AdminUser::getUsername, adminLoginForm.getUsername())
                .oneOpt();
        if (adminUserOptional.isEmpty()) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "用户不存在");
        }
        AdminUser adminUser = adminUserOptional.get();
        if (!BCrypt.checkpw(adminLoginForm.getPassword(), adminUser.getPassword())) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "密码错误");
        }
        AdminUserInfoVO adminUserInfoVO = new AdminUserInfoVO();
        adminUserInfoVO.setUserId(adminUser.getId());
        adminUserInfoVO.setUsername(adminUser.getUsername());
        adminUserInfoVO.setNickname(adminUser.getNickname());
        adminUserInfoVO.setToken(tokenService.createToken(adminUser.getId(), passwordConfig.getAdminSalt()));
        return adminUserInfoVO;
    }
}
