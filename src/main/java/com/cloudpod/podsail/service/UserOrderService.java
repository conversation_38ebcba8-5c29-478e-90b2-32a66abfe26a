package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserOrder;
import com.cloudpod.podsail.dto.order.UserOrderCreateDTO;
import com.cloudpod.podsail.dto.order.UserOrderQueryDTO;
import com.cloudpod.podsail.dto.order.UserOrderResponseDTO;
import com.cloudpod.podsail.dto.order.UserOrderUpdateDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户订单服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserOrderService {


    /**
     * 根据ID获取用户订单
     *
     * @param id 用户订单ID
     * @return 用户订单响应DTO
     */
    UserOrderResponseDTO getUserOrderById(Long id);

    /**
     * 分页查询用户订单
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserOrderResponseDTO> getUserOrderPage(UserOrderQueryDTO queryDTO);

}
