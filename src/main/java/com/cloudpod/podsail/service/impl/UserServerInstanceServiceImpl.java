package com.cloudpod.podsail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.UserServerInstanceDao;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.db.mapper.UserServerInstanceMapper;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceUpdateDTO;
import com.cloudpod.podsail.service.UserOrderService;
import com.cloudpod.podsail.service.UserServerInstanceService;
import com.cloudpod.podsail.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 用户服务器实例服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class UserServerInstanceServiceImpl   implements UserServerInstanceService {

    @Autowired
    private UserServerInstanceDao userServerInstanceDao;

    @Autowired
    private UserService userService;

    @Autowired
    private UserOrderService userOrderService;


    @Override
    public UserServerInstanceResponseDTO getUserServerInstanceById(Long id) {
        UserServerInstance userServerInstance = userServerInstanceDao.getById(id);
        return BeanCopyUtil.copyProperties(userServerInstance, UserServerInstanceResponseDTO.class);
    }

    @Override
    public IPage<UserServerInstanceResponseDTO> getUserServerInstancePage(UserServerInstanceQueryDTO queryDTO) {
        IPage<UserServerInstance> page = queryDTO.page();
        QueryWrapper<UserServerInstance> queryWrapper = buildUserServerInstanceQueryWrapper(queryDTO);
        
        IPage<UserServerInstance> userServerInstancePage = userServerInstanceDao.page(page, queryWrapper);
        
        // 转换为响应DTO
        List<UserServerInstanceResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
            userServerInstancePage.getRecords(), UserServerInstanceResponseDTO.class);
        
        IPage<UserServerInstanceResponseDTO> responsePage = new Page<>(
            userServerInstancePage.getCurrent(), userServerInstancePage.getSize(), userServerInstancePage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }

    /**
     * 构建用户服务器实例查询条件
     */
    private QueryWrapper<UserServerInstance> buildUserServerInstanceQueryWrapper(UserServerInstanceQueryDTO queryDTO) {
        QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();

        if (queryDTO.getId() != null) {
            queryWrapper.eq("id", queryDTO.getId());
        }
        if (queryDTO.getUserId() != null) {
            queryWrapper.eq("user_id", queryDTO.getUserId());
        }
        if (queryDTO.getOrderId() != null) {
            queryWrapper.eq("order_id", queryDTO.getOrderId());
        }
        if (StringUtils.hasText(queryDTO.getServerId())) {
            queryWrapper.eq("server_id", queryDTO.getServerId());
        }
        if (StringUtils.hasText(queryDTO.getServerIdLike())) {
            queryWrapper.like("server_id", queryDTO.getServerIdLike());
        }
        if (StringUtils.hasText(queryDTO.getServerName())) {
            queryWrapper.eq("server_name", queryDTO.getServerName());
        }
        if (StringUtils.hasText(queryDTO.getServerNameLike())) {
            queryWrapper.like("server_name", queryDTO.getServerNameLike());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq("server_sku_id", queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuIdLike())) {
            queryWrapper.like("server_sku_id", queryDTO.getServerSkuIdLike());
        }
        if (queryDTO.getBillingType() != null) {
            queryWrapper.eq("billing_type", queryDTO.getBillingType());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq("billing_cycle", queryDTO.getBillingCycle());
        }
        if (queryDTO.getInstanceStatus() != null) {
            queryWrapper.eq("instance_status", queryDTO.getInstanceStatus());
        }
        if (queryDTO.getStartTimeStart() != null) {
            queryWrapper.ge("start_time", queryDTO.getStartTimeStart());
        }
        if (queryDTO.getStartTimeEnd() != null) {
            queryWrapper.le("start_time", queryDTO.getStartTimeEnd());
        }
        if (queryDTO.getEndTimeStart() != null) {
            queryWrapper.ge("end_time", queryDTO.getEndTimeStart());
        }
        if (queryDTO.getEndTimeEnd() != null) {
            queryWrapper.le("end_time", queryDTO.getEndTimeEnd());
        }
        if (queryDTO.getExpireTimeStart() != null) {
            queryWrapper.ge("expire_time", queryDTO.getExpireTimeStart());
        }
        if (queryDTO.getExpireTimeEnd() != null) {
            queryWrapper.le("expire_time", queryDTO.getExpireTimeEnd());
        }
        if (queryDTO.getAutoRenew() != null) {
            queryWrapper.eq("auto_renew", queryDTO.getAutoRenew());
        }
        if (StringUtils.hasText(queryDTO.getRegionId())) {
            queryWrapper.eq("region_id", queryDTO.getRegionId());
        }
        if (StringUtils.hasText(queryDTO.getRegionIdLike())) {
            queryWrapper.like("region_id", queryDTO.getRegionIdLike());
        }
        if (StringUtils.hasText(queryDTO.getZoneId())) {
            queryWrapper.eq("zone_id", queryDTO.getZoneId());
        }
        if (StringUtils.hasText(queryDTO.getZoneIdLike())) {
            queryWrapper.like("zone_id", queryDTO.getZoneIdLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge("created_at", queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le("created_at", queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq("created_uid", queryDTO.getCreatedUid());
        }

        queryWrapper.orderByDesc("created_at");
        return queryWrapper;
    }
}
