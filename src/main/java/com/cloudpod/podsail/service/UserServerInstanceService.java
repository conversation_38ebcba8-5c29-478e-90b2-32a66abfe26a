package com.cloudpod.podsail.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.dto.instance.UserServerInstanceCreateDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceQueryDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceResponseDTO;
import com.cloudpod.podsail.dto.instance.UserServerInstanceUpdateDTO;

import java.util.List;

/**
 * 用户服务器实例服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface UserServerInstanceService {


    /**
     * 根据ID获取用户服务器实例
     *
     * @param id 用户服务器实例ID
     * @return 用户服务器实例响应DTO
     */
    UserServerInstanceResponseDTO getUserServerInstanceById(Long id);

    /**
     * 分页查询用户服务器实例
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<UserServerInstanceResponseDTO> getUserServerInstancePage(UserServerInstanceQueryDTO queryDTO);
}
