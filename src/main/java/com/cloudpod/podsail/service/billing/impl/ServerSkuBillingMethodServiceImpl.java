package com.cloudpod.podsail.service.billing.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.util.BeanCopyUtil;
import com.cloudpod.podsail.db.dao.ServerSkuBillingMethodDao;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.service.billing.ServerSkuBillingMethodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 主机套餐规格定价服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class ServerSkuBillingMethodServiceImpl implements ServerSkuBillingMethodService {
    
    @Autowired
    private ServerSkuBillingMethodDao serverSkuBillingMethodDao;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerSkuBillingMethodResponseDTO createServerSkuBillingMethod(ServerSkuBillingMethodCreateDTO createDTO) {
        // 检查定价组合是否已存在
        if (existsBillingMethod(createDTO.getSiteId(), createDTO.getServerSkuId(), createDTO.getBillingCycle())) {
            throw new PodSailException(PodSailErrorCodeEnum.SERVER_SKU_BILLING_METHOD_EXIST);
        }
        
        ServerSkuBillingMethod serverSkuBillingMethod =
                BeanCopyUtil.copyProperties(createDTO, ServerSkuBillingMethod.class);
        
        serverSkuBillingMethod.setStatus(1);
        
        serverSkuBillingMethodDao.save(serverSkuBillingMethod);
        return BeanCopyUtil.copyProperties(serverSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerSkuBillingMethodResponseDTO updateServerSkuBillingMethod(ServerSkuBillingMethodUpdateDTO updateDTO) {
        ServerSkuBillingMethod existingServerSkuBillingMethod = serverSkuBillingMethodDao.getById(updateDTO.getId());
        if (existingServerSkuBillingMethod == null) {
            throw new PodSailException(PodSailErrorCodeEnum.RESOURCE_NOT_FOUND);
        }
        
        existingServerSkuBillingMethod.setUnitPrice(updateDTO.getUnitPrice());
        existingServerSkuBillingMethod.setRemark(updateDTO.getRemark());
        serverSkuBillingMethodDao.updateById(existingServerSkuBillingMethod);
        return BeanCopyUtil.copyProperties(existingServerSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }
    
    @Override
    public ServerSkuBillingMethodResponseDTO getServerSkuBillingMethodById(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = serverSkuBillingMethodDao.getById(id);
        return BeanCopyUtil.copyProperties(serverSkuBillingMethod, ServerSkuBillingMethodResponseDTO.class);
    }
    
    @Override
    public IPage<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodPage(
            ServerSkuBillingMethodQueryDTO queryDTO) {
        IPage<ServerSkuBillingMethod> page = queryDTO.page();
        LambdaQueryWrapper<ServerSkuBillingMethod> queryWrapper = buildServerSkuBillingMethodQueryWrapper(queryDTO);
        
        IPage<ServerSkuBillingMethod> serverSkuBillingMethodPage = serverSkuBillingMethodDao.page(page, queryWrapper);
        
        // 转换为响应DTO
        List<ServerSkuBillingMethodResponseDTO> responseDTOList = BeanCopyUtil.copyPropertiesList(
                serverSkuBillingMethodPage.getRecords(), ServerSkuBillingMethodResponseDTO.class);
        
        IPage<ServerSkuBillingMethodResponseDTO> responsePage = new Page<>(
                serverSkuBillingMethodPage.getCurrent(), serverSkuBillingMethodPage.getSize(),
                serverSkuBillingMethodPage.getTotal());
        responsePage.setRecords(responseDTOList);
        
        return responsePage;
    }
    
    @Override
    public List<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodList(
            ServerSkuBillingMethodQueryDTO queryDTO) {
        LambdaQueryWrapper<ServerSkuBillingMethod> queryWrapper =
                buildServerSkuBillingMethodQueryWrapper(queryDTO);
        List<ServerSkuBillingMethod> serverSkuBillingMethodList = serverSkuBillingMethodDao.list(queryWrapper);
        return BeanCopyUtil.copyPropertiesList(serverSkuBillingMethodList, ServerSkuBillingMethodResponseDTO.class);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableServerSkuBillingMethod(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = serverSkuBillingMethodDao.getById(id);
        serverSkuBillingMethod.setStatus(1); // 1-正常
        return serverSkuBillingMethodDao.updateById(serverSkuBillingMethod);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableServerSkuBillingMethod(Long id) {
        ServerSkuBillingMethod serverSkuBillingMethod = serverSkuBillingMethodDao.getById(id);
        serverSkuBillingMethod.setStatus(2); // 2-已失效
        return serverSkuBillingMethodDao.updateById(serverSkuBillingMethod);
    }
    
    
    @Override
    public boolean existsBillingMethod(Integer siteId, String serverSkuId, String billingCycle) {
        return serverSkuBillingMethodDao.lambdaQuery().eq(ServerSkuBillingMethod::getSiteId, siteId)
                .eq(ServerSkuBillingMethod::getServerSkuId, serverSkuId)
                .eq(ServerSkuBillingMethod::getBillingCycle, billingCycle).exists();
    }
    
    /**
     * 构建主机套餐规格定价查询条件
     */
    private LambdaQueryWrapper<ServerSkuBillingMethod> buildServerSkuBillingMethodQueryWrapper(
            ServerSkuBillingMethodQueryDTO queryDTO) {
        LambdaQueryWrapper<ServerSkuBillingMethod> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryDTO.getId() != null) {
            queryWrapper.eq(ServerSkuBillingMethod::getId, queryDTO.getId());
        }
        if (StringUtils.hasText(queryDTO.getServerSkuId())) {
            queryWrapper.eq(ServerSkuBillingMethod::getServerSkuId, queryDTO.getServerSkuId());
        }
        if (StringUtils.hasText(queryDTO.getBillingCycle())) {
            queryWrapper.eq(ServerSkuBillingMethod::getBillingCycle, queryDTO.getBillingCycle());
        }
        if (queryDTO.getUnitPriceMin() != null) {
            queryWrapper.ge(ServerSkuBillingMethod::getUnitPrice, queryDTO.getUnitPriceMin());
        }
        if (queryDTO.getUnitPriceMax() != null) {
            queryWrapper.le(ServerSkuBillingMethod::getUnitPrice, queryDTO.getUnitPriceMax());
        }
        if (queryDTO.getSiteId() != null) {
            queryWrapper.eq(ServerSkuBillingMethod::getSiteId, queryDTO.getSiteId());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(ServerSkuBillingMethod::getStatus, queryDTO.getStatus());
        }
        if (StringUtils.hasText(queryDTO.getRemark())) {
            queryWrapper.eq(ServerSkuBillingMethod::getRemark, queryDTO.getRemark());
        }
        if (StringUtils.hasText(queryDTO.getRemarkLike())) {
            queryWrapper.like(ServerSkuBillingMethod::getRemark, queryDTO.getRemarkLike());
        }
        if (queryDTO.getCreatedAtStart() != null) {
            queryWrapper.ge(ServerSkuBillingMethod::getCreatedAt, queryDTO.getCreatedAtStart());
        }
        if (queryDTO.getCreatedAtEnd() != null) {
            queryWrapper.le(ServerSkuBillingMethod::getCreatedAt, queryDTO.getCreatedAtEnd());
        }
        if (queryDTO.getCreatedUid() != null) {
            queryWrapper.eq(ServerSkuBillingMethod::getCreatedUid, queryDTO.getCreatedUid());
        }
        
        queryWrapper.orderByDesc(ServerSkuBillingMethod::getCreatedAt);
        return queryWrapper;
    }
    
    @Override
    public boolean deleteServerSkuBillingMethod(Long id) {
        return serverSkuBillingMethodDao.removeById(id);
    }
    
    @Override
    public boolean deleteServerSkuBillingMethods(List<Long> ids) {
        return serverSkuBillingMethodDao.removeByIds(ids);
    }
}
