package com.cloudpod.podsail.service.billing.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.db.dao.BillingRecordDao;
import com.cloudpod.podsail.db.dao.ServerSkuBillingMethodDao;
import com.cloudpod.podsail.db.dao.UserBalanceLogDao;
import com.cloudpod.podsail.db.dao.UserDao;
import com.cloudpod.podsail.db.dao.UserServerInstanceDao;
import com.cloudpod.podsail.db.entity.BillingRecord;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.db.entity.UserBalanceLog;
import com.cloudpod.podsail.db.entity.UserServerInstance;
import com.cloudpod.podsail.service.billing.BillingEngineService;
import com.cloudpod.podsail.service.instance.InstanceLifecycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费引擎核心服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Service
public class BillingEngineServiceImpl implements BillingEngineService {

    @Autowired
    private UserServerInstanceDao userServerInstanceDao;
    
    @Autowired
    private ServerSkuBillingMethodDao serverSkuBillingMethodDao;
    
    @Autowired
    private BillingRecordDao billingRecordDao;
    
    @Autowired
    private UserBalanceLogDao userBalanceLogDao;
    
    @Autowired
    private UserDao userDao;
    
    @Autowired
    private InstanceLifecycleService instanceLifecycleService;

    // 计费配置常量 - 最小计费单位1小时

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startBilling(Long instanceId) {
        log.info("开始计费: instanceId={}", instanceId);
        
        try {
            UserServerInstance instance = getUserServerInstance(instanceId);
            // 根据SKU和计费周期获取具体的计费方式
            ServerSkuBillingMethod billingMethod = getBillingMethod(instance.getServerSkuId(), instance.getBillingCycle());
            
            // 统一预付费模式，不再区分按需付费和包年包月
            return startUnifiedPrepaidBilling(instance, billingMethod);
            
        } catch (Exception e) {
            log.error("开始计费失败: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    public int executePeriodicBilling() {
        log.info("执行周期性计费");
        
        try {
            // 查询所有运行中的实例（统一预付费模式，都需要检查续费）
            QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("instance_status", 2) // 运行中
                       .eq("is_del", 0);
            
            List<UserServerInstance> runningInstances = userServerInstanceDao.list(queryWrapper);
            
            int processedCount = 0;
            for (UserServerInstance instance : runningInstances) {
                try {
                    if (executeInstanceBilling(instance)) {
                        processedCount++;
                    }
                } catch (Exception e) {
                    log.error("实例计费失败: instanceId={}", instance.getId(), e);
                }
            }
            
            log.info("周期性计费完成: 处理实例数={}", processedCount);
            return processedCount;
            
        } catch (Exception e) {
            log.error("执行周期性计费异常", e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopBilling(Long instanceId) {
        log.info("停止计费: instanceId={}", instanceId);
        
        try {
            // 预付费模式：费用已预扣，停止时不需要额外计费，不退款
            
            // 更新结束时间
            UserServerInstance updateInstance = new UserServerInstance();
            updateInstance.setId(instanceId);
            updateInstance.setEndTime(System.currentTimeMillis());
            updateInstance.setUpdatedAt(System.currentTimeMillis());
            
            userServerInstanceDao.updateById(updateInstance);
            
            log.info("停止计费成功: instanceId={}", instanceId);
            return true;
            
        } catch (Exception e) {
            log.error("停止计费失败: instanceId={}", instanceId, e);
            return false;
        }
    }

    @Override
    public int checkAndHandleInsufficientBalance() {
        log.info("检查并处理余额不足的实例");
        
        try {
            // 查询所有运行中的实例（统一预付费模式，都需要检查余额）
            QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("instance_status", 2) // 运行中
                       .eq("is_del", 0);
            
            List<UserServerInstance> runningInstances = userServerInstanceDao.list(queryWrapper);
            
            int destroyedCount = 0;
            for (UserServerInstance instance : runningInstances) {
                try {
                    User user = userDao.getById(instance.getUserId());
                    if (user != null && user.getBalance().compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("用户余额不足，自动销毁实例: userId={}, instanceId={}, balance={}", 
                                user.getId(), instance.getId(), user.getBalance());
                        
                        if (instanceLifecycleService.destroyVirtualMachine(instance.getId(), true)) {
                            destroyedCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("处理余额不足实例失败: instanceId={}", instance.getId(), e);
                }
            }
            
            log.info("余额不足处理完成: 销毁实例数={}", destroyedCount);
            return destroyedCount;
            
        } catch (Exception e) {
            log.error("检查余额不足实例异常", e);
            return 0;
        }
    }

    @Override
    public int checkAndHandleExpiredInstances() {
        log.info("检查并处理到期的预付费实例");
        
        try {
            long currentTime = System.currentTimeMillis();
            
            // 查询所有到期的预付费实例（统一预付费模式）
            QueryWrapper<UserServerInstance> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("instance_status", 1, 2) // 创建中、运行中
                       .le("expire_time", currentTime) // 已到期
                       .gt("expire_time", 0) // 有有效期
                       .eq("is_del", 0);
            
            List<UserServerInstance> expiredInstances = userServerInstanceDao.list(queryWrapper);
            
            int destroyedCount = 0;
            for (UserServerInstance instance : expiredInstances) {
                try {
                    log.warn("预付费实例已到期，自动销毁: instanceId={}, expireTime={}", 
                            instance.getId(), instance.getExpireTime());
                    
                    if (instanceLifecycleService.destroyVirtualMachine(instance.getId(), true)) {
                        destroyedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理到期实例失败: instanceId={}", instance.getId(), e);
                }
            }
            
            log.info("到期实例处理完成: 销毁实例数={}", destroyedCount);
            return destroyedCount;
            
        } catch (Exception e) {
            log.error("检查到期实例异常", e);
            return 0;
        }
    }

    @Override
    public BigDecimal calculateInstanceCost(UserServerInstance instance, long durationMinutes) {
        try {
            // 根据SKU和计费周期获取具体的计费方式
            ServerSkuBillingMethod billingMethod = getBillingMethod(instance.getServerSkuId(), instance.getBillingCycle());
            
            // 直接返回定价表中的价格
            return billingMethod.getUnitPrice();
            
        } catch (Exception e) {
            log.error("计算实例费用失败: instanceId={}", instance.getId(), e);
            return BigDecimal.ZERO;
        }
    }

    // ================== 私有方法 ==================

    /**
     * 统一预付费计费
     * 支持：按小时/天/周/月/年/包年包月，最小单位1小时
     * 所有计费方式都是预付费模式，逻辑完全相同
     */
    private boolean startUnifiedPrepaidBilling(UserServerInstance instance, ServerSkuBillingMethod billingMethod) {
        // 统一预付费模式：预扣整个周期费用
        String billingCycle = instance.getBillingCycle();
        return chargePrepaidCycle(instance, billingMethod, billingCycle);
    }

    /**
     * 执行实例计费
     */
    private boolean executeInstanceBilling(UserServerInstance instance) {
        try {
                            // 根据SKU和计费周期获取具体的计费方式
                String billingCycle = instance.getBillingCycle();
                ServerSkuBillingMethod billingMethod = getBillingMethod(instance.getServerSkuId(), billingCycle);
                
                // 所有计费都是预付费模式，检查是否需要续费
                return checkAndRenewPrepaidInstance(instance, billingMethod, billingCycle);
            
        } catch (Exception e) {
            log.error("执行实例计费失败: instanceId={}", instance.getId(), e);
            return false;
        }
    }

    /**
     * 预付费周期计费
     * 按小时/天/周/月/年预扣整个周期费用
     */
    private boolean chargePrepaidCycle(UserServerInstance instance, ServerSkuBillingMethod billingMethod, String billingCycle) {
        try {
            // 直接使用定价表中的价格
            BigDecimal cycleCost = billingMethod.getUnitPrice();
            String description = String.format("预付费-%s周期扣费(SKU:%s)", billingCycle, billingMethod.getServerSkuId());
            
            boolean success = deductUserBalance(instance.getUserId(), cycleCost, description, instance.getId());
            
            if (success) {
                // 设置下次续费时间
                long nextRenewalTime = calculateNextRenewalTime(billingCycle);
                updateInstanceRenewalTime(instance.getId(), nextRenewalTime);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("预付费周期计费失败: instanceId={}, cycle={}", instance.getId(), billingCycle, e);
            return false;
        }
    }

    /**
     * 检查并续费预付费实例
     */
    private boolean checkAndRenewPrepaidInstance(UserServerInstance instance, ServerSkuBillingMethod billingMethod, String billingCycle) {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 检查是否需要续费（提前5分钟续费避免中断）
            if (instance.getExpireTime() > 0 && (instance.getExpireTime() - currentTime) <= 300000) {
                log.info("预付费实例即将到期，尝试自动续费: instanceId={}", instance.getId());
                return chargePrepaidCycle(instance, billingMethod, billingCycle);
            }
            
            return true; // 未到续费时间
            
        } catch (Exception e) {
            log.error("检查预付费实例续费失败: instanceId={}", instance.getId(), e);
            return false;
        }
    }

    // 按分钟计费已移除，最小计费单位为1小时

    // chargeUser方法已移除，统一使用chargePrepaidCycle预付费

    /**
     * 扣除用户余额
     */
    @Transactional(rollbackFor = Exception.class)
    private boolean deductUserBalance(Long userId, BigDecimal amount, String description, Long instanceId) {
        try {
            // 获取用户当前余额
            User user = userDao.getById(userId);
            if (user == null) {
                log.error("用户不存在: userId={}", userId);
                return false;
            }

            if (user.getBalance().compareTo(amount) < 0) {
                log.warn("用户余额不足: userId={}, balance={}, required={}", userId, user.getBalance(), amount);
                return false;
            }

            // 扣除余额
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setBalance(user.getBalance().subtract(amount));
            updateUser.setUpdatedAt(System.currentTimeMillis());
            
            userDao.updateById(updateUser);

            // 记录余额日志
            UserBalanceLog balanceLog = new UserBalanceLog();
            balanceLog.setUserId(userId);
            balanceLog.setType(2); // 2-消费
            balanceLog.setAmount(amount);
            balanceLog.setRemark(description);
            balanceLog.setCreatedAt(System.currentTimeMillis());
            balanceLog.setUpdatedAt(System.currentTimeMillis());
            
            userBalanceLogDao.save(balanceLog);

            // 记录账单 - 保存计费记录到BillingRecord表
            BillingRecord billingRecord = new BillingRecord();
            billingRecord.setUserId(userId);
            billingRecord.setInstanceId(instanceId);
            billingRecord.setAmount(amount);
            billingRecord.setBillingType(true); // true表示计费记录
            billingRecord.setStartTime(System.currentTimeMillis());
            billingRecord.setEndTime(System.currentTimeMillis());
            billingRecord.setDuration(0L); // 实时扣费，时长为0
            billingRecord.setRemark(description);
            billingRecord.setCreatedAt(System.currentTimeMillis());
            billingRecord.setUpdatedAt(System.currentTimeMillis());
            
            billingRecordDao.save(billingRecord);

            log.info("扣费成功: userId={}, amount={}, description={}", userId, amount, description);
            return true;

        } catch (Exception e) {
            log.error("扣除用户余额失败: userId={}, amount={}", userId, amount, e);
            return false;
        }
    }

    // 按需付费已统一为预付费模式，移除按分钟费用计算

    // calculateCycleCost已移除，直接使用billingMethod.getPrice()

    /**
     * 计算下次续费时间
     */
    private long calculateNextRenewalTime(String billingCycle) {
        long currentTime = System.currentTimeMillis();
        String cycle = billingCycle.toLowerCase();
        
        if (cycle.endsWith("h") || cycle.equals("hour")) {
            return currentTime + 3600000L; // +1小时
        } else if (cycle.endsWith("d") || cycle.equals("day")) {
            return currentTime + 86400000L; // +1天
        } else if (cycle.endsWith("w") || cycle.equals("week")) {
            return currentTime + 604800000L; // +1周
        } else if (cycle.endsWith("m") || cycle.equals("month")) {
            return currentTime + 2592000000L; // +30天
        } else if (cycle.endsWith("y") || cycle.equals("year")) {
            return currentTime + 31536000000L; // +365天
        } else {
            return currentTime + 3600000L; // 默认+1小时
        }
    }

    /**
     * 更新实例续费时间
     */
    private void updateInstanceRenewalTime(Long instanceId, long renewalTime) {
        UserServerInstance updateInstance = new UserServerInstance();
        updateInstance.setId(instanceId);
        updateInstance.setExpireTime(renewalTime);
        updateInstance.setUpdatedAt(System.currentTimeMillis());
        
        userServerInstanceDao.updateById(updateInstance);
    }

    // calculatePrepaidCost已移除，统一使用calculateCycleCost

    // 预付费模式不需要getLastBillingTime方法

    /**
     * 获取用户服务器实例
     */
    private UserServerInstance getUserServerInstance(Long instanceId) {
        UserServerInstance instance = userServerInstanceDao.getById(instanceId);
        if (instance == null) {
            throw new PodSailException(PodSailErrorCodeEnum.REQUEST_PARAM_ERROR, "实例不存在");
        }
        return instance;
    }

    /**
     * 获取计费方式
     */
    /**
     * 根据SKU和计费周期获取具体的计费方式
     */
    private ServerSkuBillingMethod getBillingMethod(String skuId, String billingCycle) {
        QueryWrapper<ServerSkuBillingMethod> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("server_sku_id", skuId)
                   .eq("billing_cycle", billingCycle) 
                   .eq("status", 1) // 正常状态
                   .eq("is_del", 0)
                   .orderByDesc("created_at")
                   .last("LIMIT 1");
        
        ServerSkuBillingMethod billingMethod = serverSkuBillingMethodDao.getOne(queryWrapper);
        if (billingMethod == null) {
            throw new PodSailException(PodSailErrorCodeEnum.SYSTEM_ERROR, 
                    String.format("SKU计费方式不存在: skuId=%s, billingCycle=%s", skuId, billingCycle));
        }
        return billingMethod;
    }
    
    // 已移除废弃的 getBillingMethod(String skuId) 方法
}
