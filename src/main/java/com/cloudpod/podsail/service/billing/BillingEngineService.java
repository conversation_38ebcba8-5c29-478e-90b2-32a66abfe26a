package com.cloudpod.podsail.service.billing;

import com.cloudpod.podsail.db.entity.UserServerInstance;

/**
 * 计费引擎核心服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface BillingEngineService {

    /**
     * 开始计费
     * 实例创建后立即调用，根据计费类型执行首次扣费
     *
     * @param instanceId 实例ID
     * @return 是否成功开始计费
     */
    boolean startBilling(Long instanceId);

    /**
     * 执行周期性计费
     * 由定时任务调用，扫描所有运行中的实例进行计费
     *
     * @return 处理的实例数量
     */
    int executePeriodicBilling();

    /**
     * 停止计费
     * 实例停止或销毁时调用
     *
     * @param instanceId 实例ID
     * @return 是否成功停止计费
     */
    boolean stopBilling(Long instanceId);

    /**
     * 检查并处理余额不足的实例
     * 自动销毁余额不足的按需付费实例
     *
     * @return 处理的实例数量
     */
    int checkAndHandleInsufficientBalance();

    /**
     * 检查并处理到期的包年包月实例
     * 自动销毁到期的包年包月实例
     *
     * @return 处理的实例数量
     */
    int checkAndHandleExpiredInstances();

    /**
     * 计算实例费用
     * 根据计费类型和时长计算费用
     *
     * @param instance 实例信息
     * @param durationMinutes 计费时长(分钟)
     * @return 费用金额
     */
    java.math.BigDecimal calculateInstanceCost(UserServerInstance instance, long durationMinutes);
}
