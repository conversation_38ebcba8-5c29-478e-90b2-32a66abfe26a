package com.cloudpod.podsail.service.billing;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cloudpod.podsail.db.entity.ServerSkuBillingMethod;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodCreateDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodQueryDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodResponseDTO;
import com.cloudpod.podsail.dto.billing.ServerSkuBillingMethodUpdateDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 主机套餐规格定价服务接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface ServerSkuBillingMethodService {

    /**
     * 创建主机套餐规格定价
     *
     * @param createDTO 创建主机套餐规格定价DTO
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO createServerSkuBillingMethod(ServerSkuBillingMethodCreateDTO createDTO);

    /**
     * 更新主机套餐规格定价
     *
     * @param updateDTO 更新主机套餐规格定价DTO
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO updateServerSkuBillingMethod(ServerSkuBillingMethodUpdateDTO updateDTO);
    
    /**
     * 根据ID获取主机套餐规格定价
     *
     * @param id 主机套餐规格定价ID
     * @return 主机套餐规格定价响应DTO
     */
    ServerSkuBillingMethodResponseDTO getServerSkuBillingMethodById(Long id);

    /**
     * 分页查询主机套餐规格定价
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    IPage<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodPage(ServerSkuBillingMethodQueryDTO queryDTO);
    
    List<ServerSkuBillingMethodResponseDTO> getServerSkuBillingMethodList(
            ServerSkuBillingMethodQueryDTO queryDTO);
    
    /**
     * 启用定价
     *
     * @param id 主机套餐规格定价ID
     * @return 是否启用成功
     */
    boolean enableServerSkuBillingMethod(Long id);

    /**
     * 禁用定价
     *
     * @param id 主机套餐规格定价ID
     * @return 是否禁用成功
     */
    boolean disableServerSkuBillingMethod(Long id);
    
    
    boolean existsBillingMethod(Integer siteId, String serverSkuId, String billingCycle);
    
    /**
     * 删除主机套餐规格定价
     *
     * @param id 主机套餐规格定价ID
     * @return 是否删除成功
     */
    boolean deleteServerSkuBillingMethod(@NotNull Long id);
    
    /**
     * 批量删除主机套餐规格定价
     *
     * @param ids 主机套餐规格定价ID列表
     * @return 是否删除成功
     */
    boolean deleteServerSkuBillingMethods(@Valid List<Long> ids);
}
