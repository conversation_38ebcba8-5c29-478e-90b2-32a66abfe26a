package com.cloudpod.podsail.domain.app.form;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AppUserRegisterForm
 * @date 2025/8/20 20:49
 */
@Data
public class AppUserPasswordLoginForm {
    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String username;
    @NotEmpty(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
}
