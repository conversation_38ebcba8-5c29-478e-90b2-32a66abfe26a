package com.cloudpod.podsail.domain.app.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @className AppUserInfoVo
 * @date 2025/8/20 21:07
 */
@Data
@ApiModel(value = "AppUserInfoVO", description = "用户信息")
public class AppUserInfoVO {
    @ApiModelProperty("用户ID")
    private Long id;
    @ApiModelProperty("用户唯一码")
    private String code;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("登录token")
    private String token;
    @ApiModelProperty("注册时间")
    private Long registerTime;
    @ApiModelProperty("账户余额")
    private BigDecimal balance;
}
