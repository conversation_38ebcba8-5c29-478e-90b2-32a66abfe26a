package com.cloudpod.podsail.domain.app.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AppUserRegisterForm
 * @date 2025/8/20 20:49
 */
@Data
@ApiModel(value = "忘记密码", description = "忘记密码")
public class AppUserForgetPasswordForm {
    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String username;
    @NotEmpty(message = "验证码不能为空")
    @ApiModelProperty("验证码")
    private String verifyCode;
    @NotEmpty(message = "新密码不能为空")
    @ApiModelProperty("新密码")
    private String newPassword;
}
