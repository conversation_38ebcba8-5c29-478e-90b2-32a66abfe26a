package com.cloudpod.podsail.domain.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AdminUserInfoVO
 * @date 2025/8/19 21:35
 */
@Data
@ApiModel(value = "AdminUserInfoVO", description = "登录返回模型")
public class AdminUserInfoVO {

    @ApiModelProperty("后台用户ID")
    private Long userId;
    @ApiModelProperty("后台用户名")
    private String username;
    @ApiModelProperty("用户昵称")
    private String nickname;
    @ApiModelProperty("登录token")
    private String token;

}
