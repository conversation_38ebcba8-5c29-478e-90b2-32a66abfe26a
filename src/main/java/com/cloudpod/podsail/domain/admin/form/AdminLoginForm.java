package com.cloudpod.podsail.domain.admin.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className AdminLoginForm
 * @date 2025/8/19 21:32
 */
@Data
@ApiModel(value = "AdminLoginForm", description = "后台用户登录")
public class AdminLoginForm {
    @NotEmpty(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String username;
    @NotEmpty(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
}
