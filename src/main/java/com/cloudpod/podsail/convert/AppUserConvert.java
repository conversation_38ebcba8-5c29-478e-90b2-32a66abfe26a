package com.cloudpod.podsail.convert;

import com.cloudpod.podsail.db.entity.User;
import com.cloudpod.podsail.domain.app.vo.AppUserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @className AppUserConvert
 * @date 2025/8/20 21:47
 */
@Mapper
public interface AppUserConvert {
    AppUserConvert INSTANCE = Mappers.getMapper(AppUserConvert.class);

    AppUserInfoVO appUserToInfoVO(User user);
}
