package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 用户余额流水
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("user_balance_log")
public class UserBalanceLog extends Entity<UserBalanceLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 余额类型 1-充值 2-消费 3-退款
     */
    @TableField("type")
    private Integer type;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 关联交易id,充值payment_trade表，消费billing_record表，退款refund_order表
     */
    @TableField("trade_id")
    private Long tradeId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
