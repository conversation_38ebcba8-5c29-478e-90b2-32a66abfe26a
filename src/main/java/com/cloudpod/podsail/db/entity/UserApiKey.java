package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 用户秘钥管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("user_api_key")
public class UserApiKey extends Entity<UserApiKey> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 秘钥
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 最近访问时间
     */
    @TableField("last_visit_time")
    private Long lastVisitTime;

    /**
     * 秘钥状态 1-正常 2-吊销
     */
    @TableField("status")
    private Integer status;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
