package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 用户服务器实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("user_server_instance")
public class UserServerInstance extends Entity<UserServerInstance> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 服务器实例ID(cloudpods)
     */
    @TableField("server_id")
    private String serverId;

    /**
     * 服务器名称
     */
    @TableField("server_name")
    private String serverName;

    /**
     * 服务器套餐SKU ID
     */
    @TableField("server_sku_id")
    private String serverSkuId;

    /**
     * 计费类型 1-按需付费 2-包年包月
     */
    @TableField("billing_type")
    private Integer billingType;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private String billingCycle;

    /**
     * 实例状态 1-创建中 2-运行中 3-已停止 4-已销毁
     */
    @TableField("instance_status")
    private Integer instanceStatus;

    /**
     * 开始计费时间
     */
    @TableField("start_time")
    private Long startTime;

    /**
     * 结束计费时间 0表示未结束
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 到期时间 按需付费为0
     */
    @TableField("expire_time")
    private Long expireTime;

    /**
     * 是否自动续费 0-否 1-是
     */
    @TableField("auto_renew")
    private Integer autoRenew;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;

    /**
     * 可用区ID
     */
    @TableField("zone_id")
    private String zoneId;

    /**
     * 虚拟化类型 (运行时字段，从CloudPods同步获取)
     * kvm: 虚拟机, baremetal: 裸机
     */
    @TableField(exist = false)
    private String hypervisor;

    @Override
    public Serializable pkVal() {
        return null;
    }
}
