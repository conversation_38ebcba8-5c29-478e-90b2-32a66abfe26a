package com.cloudpod.podsail.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudpod.podsail.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Getter
@Setter
@ToString
@TableName("user")
@Accessors(chain = true)
public class User extends Entity<User> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名(邮箱)
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 用户的唯一码
     */
    @TableField("code")
    private String code;

    /**
     * 账户余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private Long registerTime;

    /**
     * 站点 1-国际站 2-国内站
     */
    @TableField("size")
    private Integer site;

}
