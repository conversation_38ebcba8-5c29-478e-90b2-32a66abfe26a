package com.cloudpod.podsail.config;

import lombok.Data;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.security.Security;

/**
 * <AUTHOR>
 * @className PasswordConfig
 * @date 2025/8/18 22:16
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "password")
public class PasswordConfig {
    private String adminSalt;
    private String userSalt;
    private Integer expireTime = 300;
}
