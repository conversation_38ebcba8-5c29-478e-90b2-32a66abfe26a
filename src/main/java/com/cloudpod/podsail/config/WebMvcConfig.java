package com.cloudpod.podsail.config;

import com.cloudpod.podsail.common.interceptor.AdminUserInterceptor;
import com.cloudpod.podsail.common.interceptor.UserInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @className WevMvcConfig
 * @date 2025/8/19 20:29
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] appExcludePathPatterns = {
                "/favicon.ico",
                "/error",
                "/swagger-ui.html",
                "/swagger-resources/**",
                "/open/**",
                "/v1/login/**"
        };

        String[] adminExcludePathPatterns = {
                "/favicon.ico",
                "/error",
                "/swagger-ui.html",
                "/swagger-resources/**",
                "/admin/login"
        };

        // c端用户登录拦截器
        registry.addInterceptor(userInterceptor())
                .addPathPatterns("/v1/**")
                .excludePathPatterns(appExcludePathPatterns);

        // 后台拦截器
        registry.addInterceptor(adminUserInterceptor())
                .addPathPatterns("/admin/**")
                .excludePathPatterns(adminExcludePathPatterns);

        WebMvcConfigurer.super.addInterceptors(registry);
    }

    @Bean
    public UserInterceptor userInterceptor() {
        return new UserInterceptor();
    }

    @Bean
    public AdminUserInterceptor adminUserInterceptor() {
        return new AdminUserInterceptor();
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        configurer.setTaskExecutor(webMvcTaskExecutor());
    }

    /** 异步线程池 */
    public ThreadPoolTaskExecutor webMvcTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(16);
        threadPoolTaskExecutor.setMaxPoolSize(32);
        threadPoolTaskExecutor.setKeepAliveSeconds(60);
        threadPoolTaskExecutor.setQueueCapacity(128);
        threadPoolTaskExecutor.setThreadNamePrefix("mvc-Task-Thread-Pool");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setRejectedExecutionHandler(
                new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }
}
