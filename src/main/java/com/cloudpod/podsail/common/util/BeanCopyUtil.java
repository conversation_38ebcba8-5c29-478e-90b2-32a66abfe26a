package com.cloudpod.podsail.common.util;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean拷贝工具类
 * 用于DTO和Entity之间的转换
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public class BeanCopyUtil {

    /**
     * 单个对象拷贝
     *
     * @param source 源对象
     * @param target 目标对象类型
     * @param <S>    源对象类型
     * @param <T>    目标对象类型
     * @return 目标对象
     */
    public static <S, T> T copyProperties(S source, Class<T> target) {
        if (source == null) {
            return null;
        }
        try {
            T targetInstance = target.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, targetInstance);
            return targetInstance;
        } catch (Exception e) {
            throw new RuntimeException("对象拷贝失败", e);
        }
    }

    /**
     * 单个对象拷贝（使用Supplier）
     *
     * @param source   源对象
     * @param supplier 目标对象供应商
     * @param <S>      源对象类型
     * @param <T>      目标对象类型
     * @return 目标对象
     */
    public static <S, T> T copyProperties(S source, Supplier<T> supplier) {
        if (source == null) {
            return null;
        }
        T target = supplier.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 列表对象拷贝
     *
     * @param sources 源对象列表
     * @param target  目标对象类型
     * @param <S>     源对象类型
     * @param <T>     目标对象类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyPropertiesList(List<S> sources, Class<T> target) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        List<T> targetList = new ArrayList<>(sources.size());
        for (S source : sources) {
            T targetInstance = copyProperties(source, target);
            targetList.add(targetInstance);
        }
        return targetList;
    }

    /**
     * 列表对象拷贝（使用Supplier）
     *
     * @param sources  源对象列表
     * @param supplier 目标对象供应商
     * @param <S>      源对象类型
     * @param <T>      目标对象类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyPropertiesList(List<S> sources, Supplier<T> supplier) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        List<T> targetList = new ArrayList<>(sources.size());
        for (S source : sources) {
            T targetInstance = copyProperties(source, supplier);
            targetList.add(targetInstance);
        }
        return targetList;
    }

    /**
     * 拷贝属性到已存在的对象
     *
     * @param source 源对象
     * @param target 目标对象
     * @param <S>    源对象类型
     * @param <T>    目标对象类型
     */
    public static <S, T> void copyPropertiesToExisting(S source, T target) {
        if (source == null || target == null) {
            return;
        }
        BeanUtils.copyProperties(source, target);
    }

    /**
     * 拷贝属性到已存在的对象（忽略null值）
     *
     * @param source           源对象
     * @param target           目标对象
     * @param ignoreProperties 忽略的属性名
     * @param <S>              源对象类型
     * @param <T>              目标对象类型
     */
    public static <S, T> void copyPropertiesToExisting(S source, T target, String... ignoreProperties) {
        if (source == null || target == null) {
            return;
        }
        BeanUtils.copyProperties(source, target, ignoreProperties);
    }
}
