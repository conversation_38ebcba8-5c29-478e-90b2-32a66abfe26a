package com.cloudpod.podsail.common.util;

import cn.hutool.core.util.StrUtil;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR>
 * @className EmailSendUtil
 * @date 2025/8/20 21:16
 */
@Slf4j
@Component
public class EmailSendManage {

    @Value("${spring.mail.username}")
    private String from;
    @Autowired
    private JavaMailSender mailSender;

    public void sendSimpleEmail(String to, String subject, String text) {
        try {
            mailSender.send(message -> {
                message.setFrom(from);
                message.setRecipients(jakarta.mail.Message.RecipientType.TO, to);
                message.setSubject(subject);
                message.setText(text);
            });
        } catch (Exception e) {
            log.error("[发送邮件] 失败。 原因:{}", e.getMessage());
        }
    }

    public void sendEmailWithAttachment(String to, String subject, String text, String attachmentPath) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text);
            helper.addAttachment("Attachment", new File(attachmentPath));
            mailSender.send(message);
        } catch (Exception e) {
            log.error("[发送邮件] 失败。 原因:{}", e.getMessage());
        }


    }

    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        mailSender.send(message -> {
            message.setFrom(from);
            message.setRecipients(jakarta.mail.Message.RecipientType.TO, to);
            message.setSubject(subject);
            message.setContent(htmlContent, "text/html; charset=UTF-8");
        });
    }

    private String buildVerifyCodeHtml(String code) {
        String temp =  """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                  <meta charset="UTF-8">
                  <title>验证码邮件</title>
                </head>
                <body style="margin: 0; padding: 0; background-color: #f9f9f9; font-family: Arial, sans-serif;">
                  <table role="presentation" style="width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <!-- 顶部 -->
                    <tr>
                      <td style="background-color: #007BFF; color: #ffffff; padding: 20px; text-align: center; font-size: 24px; font-weight: bold;">
                        您的验证码
                      </td>
                    </tr>
                    <!-- 主体 -->
                    <tr>
                      <td style="padding: 30px 20px; text-align: center;">
                        <p style="font-size: 16px; color: #555; line-height: 1.5;">
                          您正在使用邮箱验证，请在 <strong style="color: #007BFF;">30分钟内</strong> 完成操作。
                        </p>
                        <div style="margin: 30px 0; font-size: 32px; font-weight: bold; letter-spacing: 5px; color: #007BFF;">
                          {code}
                        </div>
                        <p style="font-size: 14px; color: #888;">
                          请勿泄露验证码给他人，如非本人操作请忽略此邮件。
                        </p>
                      </td>
                    </tr>
                    <tr>
                      <td style="background-color: #f0f0f0; padding: 20px; text-align: center; font-size: 12px; color: #999;">
                        &copy; 2025 您的公司名称. 保留所有权利。
                        <br>
                        <a href="https://yourcompany.com" style="color: #007BFF; text-decoration: none;">访问官网</a>
                      </td>
                    </tr>
                  </table>
                </body>
                </html>
                """;
        return StrUtil.format(temp, code);
    }

    public void sendVerifyCode(String code, String to) {
        sendHtmlEmail(to, "邮箱验证码", buildVerifyCodeHtml(code));
    }
}
