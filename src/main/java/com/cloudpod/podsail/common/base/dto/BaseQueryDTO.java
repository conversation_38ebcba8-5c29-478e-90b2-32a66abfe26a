package com.cloudpod.podsail.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 基础查询DTO
 * 用于查询实体的请求参数
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public abstract class BaseQueryDTO extends BasePageQuery {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("创建时间开始")
    private Long createdAtStart;

    @ApiModelProperty("创建时间结束")
    private Long createdAtEnd;

    @ApiModelProperty("更新时间开始")
    private Long updatedAtStart;

    @ApiModelProperty("更新时间结束")
    private Long updatedAtEnd;

    @ApiModelProperty("创建人ID")
    private Long createdUid;

    @ApiModelProperty("更新人ID")
    private Long updatedUid;
}
