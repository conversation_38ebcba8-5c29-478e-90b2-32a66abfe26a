package com.cloudpod.podsail.common.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;

/**
 * 基础创建DTO
 * 用于创建实体的请求参数
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public abstract class BaseCreateDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 1L;

    // 创建时不需要ID，由系统自动生成
    // 创建时间、创建人等字段由系统自动填充
}
