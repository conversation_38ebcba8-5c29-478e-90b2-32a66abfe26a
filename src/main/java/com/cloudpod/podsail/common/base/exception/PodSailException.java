package com.cloudpod.podsail.common.base.exception;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;

@EqualsAndHashCode(callSuper = true)
@Data
public class PodSailException extends RuntimeException {

    private static final long serialVersionUID = - 82648410L;

    /**
     * 错误码
     */
    private int code;

    /**
     * 错误类型
     */
    private String type;

    /**
     * 错误信息
     */

    private String msg;

    /**
     * msg参数替换
     */
    private Object[] args;

    public PodSailException(ErrorCode errorCode) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
        this.type = errorCode.getType().type;
        this.msg = errorCode.getMsg();
    }

    public PodSailException(ErrorCode errorCode, Object ... args) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
        this.type = errorCode.getType().type;
        this.msg = getMessage();
        this.args = args;
    }

    public PodSailException(int code, String type, String msg, Object ... args) {
        super(msg);
        this.code = code;
        this.type = type;
        this.msg = msg;
        this.args = args;
    }

    public PodSailException(PodSailErrorCodeEnum code, String msg) {
        super(msg);
        this.code = code.getCode();
        this.msg = msg;
    }

    private PodSailException(int code, String type, String msg, Throwable e, Object ... args) {
        super(msg, e);
        this.code = code;
        this.type = type;
        this.msg = msg;
        this.args = args;
    }

    public static PodSailException wrap(Exception e) {
        if (e instanceof PodSailException) {
            return (PodSailException) e;
        }
        return PodSailException.throwException(PodSailErrorCodeEnum.SYSTEM_ERROR, e.getMessage());
    }

    public String getMsg() {
        return StrUtil.format(msg, args);
    }

    @Override
    public String getMessage() {
        return getMsg();
    }

    @Override
    public String toString() {
        return "PodSailException code: " + code + ", type: " + type + ", msg: " + getMsg();
    }


    public static PodSailException throwException(ErrorCode errorCode, String msg) {
        PodSailException PodSailException = new PodSailException(errorCode);
        PodSailException.setMsg(errorCode.getMsg() + ", " + msg);
        return PodSailException;
    }

    public static PodSailException throwException(ErrorCode errorCode) {
        return new PodSailException(errorCode);
    }

    public static PodSailException throwException(PodSailErrorCodeEnum TechErrorCodeEnum) {
        return new PodSailException(TechErrorCodeEnum);
    }

    public static Builder<?> construct() {
        return new Builder<>(BaseErrorCodeEnum.SYSTEM_ERROR, null);
    }

    public static Builder<?> construct(Throwable e) {
        return new Builder<>(BaseErrorCodeEnum.SYSTEM_ERROR, e);
    }

    public static <T extends Enum<T> & ErrorCode> Builder<T> construct(T expEnum) {
        return new Builder<>(expEnum, null);
    }

    public static <T extends Enum<T> & ErrorCode> Builder<T> construct(T expEnum, Throwable e) {
        return new Builder<>(expEnum, e);
    }

    public static <T extends Enum<T> & ErrorCode> Builder<T> constructWithArgs(T expEnum, Object... args) {
        return new Builder<>(expEnum, null).withArgs(args);
    }

    /**
     * 构建
     */
    public static class Builder<T extends Enum<T> & ErrorCode> {

        private int code;
        private String type;
        private String msg;
        private StringBuilder msgBuilder;
        private Throwable e;
        private Object[] args = new Object[]{};

        private Builder(T expEnum, Throwable e) {
            this.code = expEnum.getCode();
            this.type = expEnum.getType().name();
            this.msg = expEnum.getMsg();
            this.msgBuilder = new StringBuilder();
            this.e = e;
        }

        public Builder<T> withArgs(Object... params) {
            this.args = params;
            return this;
        }

        public Builder<T> setMsg(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder<T> appendMsg(String otherMsg) {
            this.msgBuilder.append(otherMsg);
            return this;
        }

        /**
         * 格式化enum
         * @param params
         * @return
         */
        public Builder<T> format(Object ...params) {
            this.msg = String.format(msg, params);
            return this;
        }

        public PodSailException build() {
            String msg = this.msg;
            String otherMsg = msgBuilder.toString();
            if (StrUtil.isNotBlank(otherMsg)) {
                if (StrUtil.isNotBlank(msg)) {
                    msg = msg.concat(", ").concat(otherMsg);
                } else {
                    msg = otherMsg;
                }
            }
            if (e == null) {
                return new PodSailException(this.code, this.type, msg, args);
            } else {
                if (e instanceof PodSailException) {
                    return (PodSailException) e;
                }
                if (e instanceof UndeclaredThrowableException) {
                    Throwable targetException = ((InvocationTargetException) e.getCause()).getTargetException();
                    if (targetException instanceof PodSailException) {
                        return (PodSailException) targetException;
                    }
                }
                if (e instanceof InvocationTargetException) {
                    Throwable targetException = ((InvocationTargetException) e).getTargetException();
                    if (targetException instanceof PodSailException) {
                        return (PodSailException) targetException;
                    }
                }
                return new PodSailException(this.code, this.type, msg, e, args);
            }
        }
    }
}
