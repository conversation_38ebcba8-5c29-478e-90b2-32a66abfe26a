package com.cloudpod.podsail.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.session.admin.AdminUserSession;
import com.cloudpod.podsail.common.session.admin.AdminUserSessionHolder;
import com.cloudpod.podsail.common.session.app.UserSession;
import com.cloudpod.podsail.common.session.app.UserSessionHolder;
import com.cloudpod.podsail.config.PasswordConfig;
import com.cloudpod.podsail.service.auth.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;


@Slf4j
public class UserInterceptor implements HandlerInterceptor {

    @Autowired
    private PasswordConfig passwordConfig;
    @Autowired
    private TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        UserSessionHolder.clear();
        String authorization = request.getHeader("Authorization");
        String site = request.getHeader("x-site");
        if (StrUtil.isBlank(authorization)) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.TOKEN_NOT_FOUND);
        }
        if (StrUtil.isBlank(site)) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.BUSINESS_ERROR, "站点不能为空");
        }
        Long userId = tokenService.verifyToken(authorization, passwordConfig.getUserSalt());
        UserSessionHolder.setSession(UserSession.of(userId, Integer.parseInt(site)));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }
}
