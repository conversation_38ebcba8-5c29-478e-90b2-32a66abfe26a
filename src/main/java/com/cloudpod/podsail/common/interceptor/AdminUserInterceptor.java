package com.cloudpod.podsail.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.cloudpod.podsail.common.base.exception.PodSailErrorCodeEnum;
import com.cloudpod.podsail.common.base.exception.PodSailException;
import com.cloudpod.podsail.common.session.admin.AdminUserSession;
import com.cloudpod.podsail.common.session.admin.AdminUserSessionHolder;
import com.cloudpod.podsail.config.PasswordConfig;
import com.cloudpod.podsail.service.auth.TokenService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;


@Slf4j
public class AdminUserInterceptor implements HandlerInterceptor {

    @Autowired
    private PasswordConfig passwordConfig;
    @Autowired
    private TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AdminUserSessionHolder.clear();
        String authorization = request.getHeader("Authorization");
        if (StrUtil.isBlank(authorization)) {
            throw PodSailException.throwException(PodSailErrorCodeEnum.TOKEN_NOT_FOUND);
        }
        Long userId = tokenService.verifyToken(authorization, passwordConfig.getAdminSalt());
        AdminUserSessionHolder.setSession(AdminUserSession.of(userId));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        AdminUserSessionHolder.clear();
    }
}
