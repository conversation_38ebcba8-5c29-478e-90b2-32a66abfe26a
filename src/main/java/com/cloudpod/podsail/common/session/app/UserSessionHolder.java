package com.cloudpod.podsail.common.session.app;

import java.util.Optional;

/**
 * <AUTHOR>
 * @className UserSessionHolder
 * @date 2025/8/19 21:23
 */
public class UserSessionHolder {

    private final ThreadLocal<UserSession> USER_SESSION_THREAD_LOCAL = new ThreadLocal<>();

    private static final UserSessionHolder INSTANCE = new UserSessionHolder();

    private UserSessionHolder() {
    }

    /**
     * 设置用户Session
     *
     * @param userSession 用户信息
     */
    public static void setSession(UserSession userSession) {
        INSTANCE.USER_SESSION_THREAD_LOCAL.set(userSession);
    }

    /**
     * 获取用户Session
     *
     * @return UserSession
     */
    public static UserSession getSession() {
        return INSTANCE.USER_SESSION_THREAD_LOCAL.get();
    }

    /**
     * 获取Session中的用户信息
     *
     * @return userId
     */
    public static Long getUserId() {
        return Optional.ofNullable(getSession()).map(UserSession::getUserId).orElse(0L);
    }

    /**
     * 是否已登录
     */
    public static boolean isLogin() {
        return getUserId() > 0;
    }

    /**
     * 清空Session
     */
    public static void clear() {
        INSTANCE.USER_SESSION_THREAD_LOCAL.remove();
    }
}
