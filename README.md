# PodSail 云主机管理系统

PodSail 是一个基于 Spring Boot 的云主机管理系统，提供用户管理、订单管理、服务器实例管理、计费管理等功能。

## 项目结构

```
src/main/java/com/cloudpod/podsail/
├── common/                 # 公共模块
│   ├── base/              # 基础类
│   │   ├── dto/           # 基础DTO类
│   │   ├── exception/     # 异常处理
│   │   └── response/      # 响应封装
│   └── util/              # 工具类
├── config/                # 配置类
├── controller/            # 控制器层
├── db/                    # 数据库相关
│   ├── dao/              # 数据访问层
│   ├── entity/           # 实体类
│   └── mapper/           # MyBatis Mapper
├── dto/                   # 数据传输对象
│   ├── billing/          # 计费相关DTO
│   ├── instance/         # 实例相关DTO
│   ├── order/            # 订单相关DTO
│   └── user/             # 用户相关DTO
└── service/               # 服务层
    ├── base/             # 基础服务
    └── impl/             # 服务实现
```

## 主要功能模块

### 1. 用户管理模块
- 用户CRUD操作
- 用户API密钥管理
- 用户余额流水管理

### 2. 订单管理模块
- 订单创建、支付、取消、退款
- 订单状态管理
- 订单统计分析

### 3. 服务器实例管理模块
- 服务器实例生命周期管理
- 实例状态监控
- 自动续费管理

### 4. 计费管理模块
- 计费记录生成
- 扣费处理
- 计费统计分析

### 5. 定价管理模块
- 服务器套餐定价配置
- 多区域定价支持
- 定价生效时间管理

## 技术栈

- **框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.x
- **API文档**: Swagger 2
- **构建工具**: Maven
- **Java版本**: JDK 8+

## 快速开始

### 1. 环境准备
- JDK 8 或更高版本
- MySQL 8.0
- Maven 3.6+

### 2. 数据库初始化
执行 `database/init.sql` 文件创建数据库表结构。

### 3. 配置修改
修改 `src/main/resources/application.yml` 中的数据库连接配置：

```yaml
spring:
  datasource:
    url: *************************************************************************************************************************************************
    username: your_username
    password: your_password
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问API文档
启动后访问: http://localhost:8080/swagger-ui.html

## API接口说明

### 用户管理接口
- `POST /api/users` - 创建用户
- `GET /api/users/{id}` - 查询用户
- `PUT /api/users` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 订单管理接口
- `POST /api/user-orders` - 创建订单
- `POST /api/user-orders/{id}/pay` - 支付订单
- `POST /api/user-orders/{id}/cancel` - 取消订单

### 服务器实例管理接口
- `POST /api/user-server-instances` - 创建实例
- `POST /api/user-server-instances/{id}/start` - 启动实例
- `POST /api/user-server-instances/{id}/stop` - 停止实例

### 计费管理接口
- `POST /api/billing-records` - 创建计费记录
- `POST /api/billing-records/{id}/charge-success` - 标记扣费成功

## 数据库表说明

### 核心表
- `user` - 用户表
- `user_api_key` - 用户API密钥表
- `user_balance_log` - 用户余额流水表
- `user_order` - 用户订单表
- `user_server_instance` - 用户服务器实例表
- `server_sku_billing_method` - 服务器套餐定价表
- `billing_record` - 计费记录表

## 开发规范

### 代码结构
- Controller层：处理HTTP请求，参数校验
- Service层：业务逻辑处理
- DAO层：数据访问
- DTO层：数据传输对象

### 异常处理
- 使用统一的异常处理机制
- 业务异常使用 `PodSailException`
- 全局异常处理器 `GlobalExceptionHandler`

### 响应格式
所有API接口统一使用 `Response<T>` 格式返回：
```json
{
  "code": 200,
  "message": "success",
  "data": {...}
}
```

## CloudPods Java SDK 问题解决

如果使用 Java 9+ 版本，可能会遇到模块化系统（JPMS）限制导致的错误：

```
java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.String java.net.HttpURLConnection.method accessible: module java.base does not "opens java.net" to unnamed module
```

**解决方案：**

启动时添加以下JVM参数：
```bash
java --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED -jar server.jar
```

在IDEA中，可以在 Run Configuration 的 VM options 中添加这些参数。



## Oracle JDK21在使用过程中遇到的问题
> cn.hutool.crypto.CryptoException: SecurityException: JCE cannot authenticate the provider BC

**解决方案：**
> 切换idea的jdk => Eclipse temurin JDK21.0.8

## 许可证

本项目采用 MIT 许可证。


